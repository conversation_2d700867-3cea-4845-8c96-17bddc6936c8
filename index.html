<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent Lee™ System</title>
  <style>
    /* Basic Reset */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      background-color: #0a0e29; /* Dark blue background */
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      position: relative;
      overflow: hidden;
    }
    
    /* Animated background grid */
    .grid-background {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
          linear-gradient(rgba(0, 242, 255, 0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 242, 255, 0.1) 1px, transparent 1px);
      background-size: 50px 50px;
      z-index: -1;
      animation: gridMove 20s linear infinite;
    }

    @keyframes gridMove {
      0% { transform: translate(0, 0); }
      100% { transform: translate(50px, 50px); }
    }
    
    /* Boot screen overlay */
    .boot-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(10, 14, 41, 0.95);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 2000;
      transition: opacity 1s ease;
    }

    .boot-overlay.hidden {
      opacity: 0;
      pointer-events: none;
    }

    .boot-logo {
      font-size: 4rem;
      font-weight: 900;
      color: #00f2ff;
      text-shadow: 0 0 30px rgba(0, 242, 255, 0.6);
      margin-bottom: 2rem;
      animation: pulse 2s infinite;
    }
    
    .boot-logo-image {
      width: 120px;
      height: 120px;
      margin-bottom: 2rem;
      animation: pulse 2s infinite;
      border-radius: 20px;
      box-shadow: 0 0 30px rgba(0, 242, 255, 0.6);
    }

    .boot-status {
      font-size: 1.2rem;
      color: #e0e7ff;
      margin-bottom: 3rem;
      text-align: center;
    }

    .boot-progress {
      width: 400px;
      height: 4px;
      background: rgba(0, 242, 255, 0.2);
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 2rem;
    }

    .boot-progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #00f2ff, #9854ff);
      width: 0%;
      border-radius: 2px;
      transition: width 0.5s ease;
    }

    .boot-console {
      width: 500px;
      height: 200px;
      background: rgba(0, 0, 0, 0.8);
      border: 1px solid #00f2ff;
      border-radius: 8px;
      padding: 15px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      overflow-y: auto;
      color: #e0e7ff;
    }

    .console-line {
      margin-bottom: 5px;
      opacity: 0;
      animation: fadeIn 0.5s forwards;
    }

    .console-line.success { color: #06d6a0; }
    .console-line.warning { color: #ffd166; }
    .console-line.error { color: #ff416c; }
    .console-line.info { color: #00f2ff; }

    @keyframes fadeIn {
      to { opacity: 1; }
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    /* Window Controls on Card */
    .window-controls {
      position: absolute;
      top: 15px;
      right: 15px;
      display: flex;
      gap: 5px;
      z-index: 10000;
      -webkit-app-region: no-drag;
    }

    .window-btn {
      width: 25px;
      height: 25px;
      border: none;
      border-radius: 50%;
      cursor: pointer;
      font-size: 14px;
      font-weight: bold;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .minimize-btn {
      background: #ffbd2e;
    }

    .minimize-btn:hover {
      background: #e6a82a;
      transform: scale(1.1);
    }

    .close-btn {
      background: #ff5f56;
    }

    .close-btn:hover {
      background: #e6544b;
      transform: scale(1.1);
    }

    /* Minimized Bubble */
    .minimized-bubble {
      position: fixed;
      top: 50px;
      right: 50px;
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      cursor: pointer;
      display: none;
      align-items: center;
      justify-content: center;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      z-index: 9999;
      transition: all 0.3s ease;
      border: 3px solid rgba(255,255,255,0.3);
    }

    .minimized-bubble:hover {
      transform: scale(1.1);
      box-shadow: 0 15px 40px rgba(0,0,0,0.4);
    }

    .bubble-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
    }

    .bubble-pulse {
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: rgba(255,255,255,0.3);
      animation: bubblePulse 2s infinite;
    }

    @keyframes bubblePulse {
      0% { transform: scale(1); opacity: 1; }
      100% { transform: scale(1.2); opacity: 0; }
    }

    /* Main Container - Floating Widget Style */
    body {
      margin: 0;
      padding: 0;
      background: transparent;
      overflow: hidden;
      -webkit-app-region: no-drag;
    }

    .grid-background {
      display: none; /* Hide grid for floating widget */
    }

    /* Perfect Window Sizing - Card Only */
    body {
      margin: 0;
      padding: 0;
      width: 390px;
      height: 590px;
      overflow: hidden;
      background: transparent;
    }

    /* Agent Card Styles - PERFECT CARD-ONLY VIEW */
    #agent-card {
      width: 390px;
      height: 590px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 20px;
      border: 2px solid rgba(255,255,255,0.3);
      box-shadow: 0 20px 40px rgba(0,0,0,0.3);
      position: fixed;
      top: 0;
      left: 0;
      padding: 20px;
      z-index: 1000;
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-app-region: no-drag;
      box-sizing: border-box;
    }
    
    /* Card Header */
    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;
      cursor: move;
      margin-bottom: 16px;
    }
    
    .avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 2px solid #93c5fd;
      box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
    }
    
    .avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .agent-details h3 {
      color: #93c5fd;
      font-size: 18px;
      margin-bottom: 4px;
    }
    
    .agent-details p {
      color: #bfdbfe;
      font-size: 14px;
    }
    
    /* Navigation Grid */
    .navigation-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      margin-bottom: 16px;
    }
    
    .nav-button {
      background-color: #334155;
      border: none;
      color: white;
      padding: 8px 4px;
      text-align: center;
      text-decoration: none;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .nav-button:hover {
      background-color: #475569;
    }
    
    .nav-button span {
      font-size: 16px;
      margin-bottom: 4px;
      color: #60a5fa;
    }
    
    /* Chat Area */
    .chat-area {
      height: 144px;
      background-color: #334155;
      border-radius: 8px;
      padding: 8px;
      margin-bottom: 8px;
      overflow-y: auto;
    }
    
    .message {
      padding: 8px;
      margin-bottom: 8px;
      border-radius: 8px;
    }
    
    .user-message {
      background-color: #475569;
      margin-left: 16px;
    }
    
    .agent-message {
      background-color: #3b82f6;
      margin-right: 16px;
    }
    
    .empty-chat {
      color: #94a3b8;
      text-align: center;
      font-style: italic;
      margin-top: 48px;
    }
    
    /* Message Input */
    .message-input {
      width: 100%;
      padding: 8px;
      border-radius: 8px;
      border: 1px solid #475569;
      background-color: #475569;
      color: white;
      resize: none;
      margin-bottom: 12px;
    }
    
    .message-input::placeholder {
      color: #94a3b8;
    }
    
    /* Control Buttons */
    .control-row {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;
      margin-bottom: 8px;
    }
    
    .control-button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 4px;
      border: none;
      border-radius: 8px;
      color: white;
      font-size: 12px;
      cursor: pointer;
    }
    
    .send-btn { background-color: #2563eb; }
    .send-btn:hover { background-color: #3b82f6; }
    
    .listen-btn { background-color: #16a34a; }
    .listen-btn:hover { background-color: #22c55e; }
    .listen-btn.active { background-color: #22c55e; box-shadow: 0 0 10px #22c55e; }
    
    .stop-btn { background-color: #dc2626; }
    .stop-btn:hover { background-color: #ef4444; }
    
    .finish-btn { background-color: #ca8a04; }
    .finish-btn:hover { background-color: #eab308; }
    
    .email-btn { background-color: #4f46e5; }
    .email-btn:hover { background-color: #6366f1; }
    
    .phone-btn { background-color: #0d9488; }
    .phone-btn:hover { background-color: #14b8a6; }
    
    .chat-btn { background-color: #3b82f6; }
    .chat-btn:hover { background-color: #60a5fa; }
    
    .camera-btn { background-color: #9333ea; }
    .camera-btn:hover { background-color: #a855f7; }
    
    /* Comprehensive Permissions Modal */
    .permissions-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.95);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 3000;
      opacity: 1;
      visibility: visible;
      transition: all 0.3s ease;
    }

    .permissions-modal.hidden {
      opacity: 0;
      visibility: hidden;
    }

    .permissions-content {
      background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
      border-radius: 20px;
      padding: 30px;
      max-width: 500px;
      width: 90%;
      border: 2px solid #00f2ff;
      box-shadow: 0 20px 40px rgba(0, 242, 255, 0.3);
    }

    

    .modal-title {
      font-size: 1.8rem;
      margin-bottom: 20px;
      color: #00f2ff;
    }

    .modal-description {
      margin-bottom: 30px;
      line-height: 1.6;
      color: rgba(224, 231, 255, 0.9);
    }

    .permission-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px;
      margin-bottom: 10px;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      border: 1px solid rgba(0, 242, 255, 0.2);
      color: white; /* Added white text color */
    }

    .permission-item strong {
      color: #e0e7ff; /* Lighter color for strong text */
      font-weight: 600;
    }

    .permission-item small {
      color: #bfdbfe; /* Light blue for small description text */
      display: block;
      margin-top: 5px;
    }

    .permission-toggle {
      width: 50px;
      height: 25px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 15px;
      position: relative;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .permission-toggle.active {
      background: #06d6a0;
    }

    .permission-toggle::after {
      content: '';
      position: absolute;
      width: 21px;
      height: 21px;
      background: white;
      border-radius: 50%;
      top: 2px;
      left: 2px;
      transition: transform 0.3s ease;
    }

    .permission-toggle.active::after {
      transform: translateX(25px);
    }

    .modal-buttons {
      display: flex;
      gap: 15px;
      margin-top: 30px;
    }

    .modal-button {
      flex: 1;
      padding: 12px;
      border: none;
      border-radius: 10px;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .modal-button.primary {
      background: linear-gradient(135deg, #00f2ff, #9854ff);
      color: white;
    }

    .modal-button.secondary {
      background: transparent;
      color: #e0e7ff;
      border: 1px solid rgba(0, 242, 255, 0.2);
    }

    .modal-button:hover {
      transform: translateY(-2px);
    }

    /* Help link for permissions */
    .help-link {
      color: #00f2ff;
      text-decoration: underline;
      cursor: pointer;
      margin-top: 10px;
      display: inline-block;
      font-size: 0.9rem;
    }

    .help-link:hover {
      text-decoration: none;
      color: #9854ff;
    }
    
    /* Camera feed */
    .camera-container {
      position: fixed;
      top: 20px;
      right: 20px;
      width: 320px;
      height: 240px;
      border-radius: 10px;
      overflow: hidden;
      border: 2px solid #3b82f6;
      z-index: 900;
      display: none;
    }
    
    .camera-container.active {
      display: block;
    }
    
    #camera-feed {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    /* System status indicator */
    
    
    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #06d6a0;
      box-shadow: 0 0 10px #06d6a0;
    }
    
    .status-dot.warning {
      background: #ffd166;
      box-shadow: 0 0 10px #ffd166;
    }
    
    .status-dot.error {
      background: #ff416c;
      box-shadow: 0 0 10px #ff416c;
    }
  </style>
</head>
<body>
  <!-- Window Controls for Floating Widget -->
  <div class="window-controls">
    <button type="button" class="window-btn minimize-btn" id="minimizeBtn" title="Minimize">−</button>
    <button type="button" class="window-btn close-btn" id="closeBtn" title="Close">×</button>
  </div>

  <!-- Minimized State -->
  <div class="minimized-bubble" id="minimizedBubble">
    <img src="frontend/AgetleeAvatar.png" alt="Agent Lee" class="bubble-avatar">
    <div class="bubble-pulse"></div>
  </div>

  <div class="grid-background"></div>

  <!-- Boot Screen -->
  <div class="boot-overlay" id="bootOverlay">
    <img src="frontend/AgetleeAvatar.png" alt="Agent Lee" class="boot-logo-image">
    <div class="boot-status" id="bootStatus">Initializing Agent Lee Protocol...</div>
    <div class="boot-progress">
      <div class="boot-progress-bar" id="bootProgress"></div>
    </div>
    <div class="boot-console" id="bootConsole">
      <div class="console-line info">[SYSTEM] Starting Agent Lee™ Operating System...</div>
    </div>
  </div>
  
  <!-- System status indicator -->
  <div class="system-status" id="systemStatus">
    <div class="status-dot" id="statusDot"></div>
    <span id="statusText">Initializing...</span>
  </div>
  
  <!-- Agent Card - Main Interface -->
  <div id="agent-card">
    <!-- Card Header -->
    <div class="card-header" id="drag-handle">
      <div class="avatar">
        <img src="frontend/AgetleeAvatar.png" alt="Agent Lee" id="agent-avatar">
      </div>
      <div class="agent-details">
        <h3>Agent Lee</h3>
        <p>Your AI Assistant</p>
      </div>
    </div>
    
    <!-- Navigation Grid -->
    <div class="navigation-grid">
      <button type="button" class="nav-button" data-dashboard="todo">
        <span>📋</span>
        To-Do List
      </button>
      <button type="button" class="nav-button" data-dashboard="agents">
        <span>👥</span>
        Agents
      </button>
      <button type="button" class="nav-button" data-dashboard="database">
        <span>🗄️</span>
        Database
      </button>
      <button type="button" class="nav-button" data-dashboard="llm">
        <span>🧠</span>
        LLM Brain
      </button>
      <button type="button" class="nav-button" data-dashboard="workers">
        <span>⚙️</span>
        Workers
      </button>
      <button type="button" class="nav-button" id="diagnosticsBtn">
        <span>🔍</span>
        Diagnostics
      </button>
    </div>
    
    <!-- Chat Area -->
    <div class="chat-area" id="chat-messages">
      <div class="empty-chat" id="empty-message">
        Agent Lee initialized and ready to assist
      </div>
      <!-- Messages will be added here dynamically -->
    </div>
    
    <!-- Message Input -->
    <textarea 
      class="message-input" 
      id="message-input" 
      rows="2" 
      placeholder="Type your message or command..."></textarea>
    
    <!-- Control Buttons - First Row -->
    <div class="control-row">
      <button type="button" class="control-button send-btn" id="send-button">
        <span class="icon">✉️</span> Send
      </button>
      <button type="button" class="control-button listen-btn" id="listen-button">
        <span class="icon">🎤</span> Listen
      </button>
      <button type="button" class="control-button stop-btn" id="stop-button">
        <span class="icon">⏹️</span> Stop
      </button>
      <button type="button" class="control-button finish-btn" id="finish-button">
        <span class="icon">✅</span> Finish
      </button>
    </div>

    <!-- Control Buttons - Second Row -->
    <div class="control-row">
      <button type="button" class="control-button email-btn" id="email-button">
        <span class="icon">📧</span> Email
      </button>
      <button type="button" class="control-button phone-btn" id="phone-button">
        <span class="icon">📱</span> Phone
      </button>
      <button type="button" class="control-button chat-btn" id="chat-button">
        <span class="icon">💬</span> Chat
      </button>
      <button type="button" class="control-button camera-btn" id="camera-button">
        <span class="icon">📷</span> Camera
      </button>
    </div>

    <!-- Personality Controls -->
    <div class="personality-panel">
      <h3>🎭 Agent Lee's Personality</h3>
      <div class="personality-controls">
        <label for="personality-select">Current Mode:</label>
        <select id="personality-select">
          <option value="default">Default - Balanced & Helpful</option>
          <option value="funny">Funny - Jokes & Humor</option>
          <option value="serious">Serious - Professional</option>
          <option value="excited">Excited - Enthusiastic</option>
          <option value="chill">Chill - Laid-back</option>
        </select>
        <button type="button" id="memory-button" class="personality-btn">
          <span class="icon">🧠</span> View Memory
        </button>
      </div>
      <div id="personality-status" class="personality-status"></div>
    </div>
  </div>

  <!-- Comprehensive Permissions Modal -->
  <div class="permissions-modal" id="permissionsModal">
    <div class="permissions-content">
      <h2 class="modal-title">🤖 Agent Lee™ System Permissions</h2>
      <p class="modal-description">
        Welcome! Agent Lee™ needs comprehensive permissions to serve as your intelligent assistant.
        Grant all permissions for the best experience.
      </p>

      <div class="permission-item">
        <div>
          <strong>🎤 Microphone Access</strong>
          <br><small>Essential for voice commands, conversations, and interruption handling</small>
        </div>
        <div class="permission-toggle active" data-permission="microphone"></div>
      </div>

      <div class="permission-item">
        <div>
          <strong>📷 Camera Access</strong>
          <br><small>For visual interactions, facial recognition, and enhanced AI responses</small>
        </div>
        <div class="permission-toggle active" data-permission="camera"></div>
      </div>

      <div class="permission-item">
        <div>
          <strong>🔔 Notifications</strong>
          <br><small>System alerts, reminders, and background task notifications</small>
        </div>
        <div class="permission-toggle active" data-permission="notifications"></div>
      </div>

      <div class="permission-item">
        <div>
          <strong>💾 Local Storage</strong>
          <br><small>Remember preferences, conversation history, and personalization</small>
        </div>
        <div class="permission-toggle active" data-permission="storage"></div>
      </div>

      <div class="permission-item">
        <div>
          <strong>🌐 Network Access</strong>
          <br><small>Connect to AI services, web search, and real-time data</small>
        </div>
        <div class="permission-toggle active" data-permission="network"></div>
      </div>

      <div class="permission-item">
        <div>
          <strong>📱 System Control</strong>
          <br><small>Open applications, manage files, and system automation</small>
        </div>
        <div class="permission-toggle active" data-permission="system"></div>
      </div>

      <div class="permission-item">
        <div>
          <strong>🧠 Background Processing</strong>
          <br><small>Continuous learning, optimization, and proactive assistance</small>
        </div>
        <div class="permission-toggle active" data-permission="background"></div>
      </div>

      <div class="modal-buttons">
        <button type="button" class="modal-button secondary" id="cancelPermissions">Minimal Setup</button>
        <button type="button" class="modal-button primary" id="savePermissions">🚀 Grant All & Start Agent Lee</button>
      </div>
    </div>
  </div>

  <!-- Camera Container -->
  <div class="camera-container" id="cameraContainer">
    <video id="camera-feed" autoplay muted></video>
  </div>

  <!-- JavaScript -->
  <script type="module">
    // Import API client
    import apiClient from './js/api-client.js';

    // Global state
    let isListening = false;
    let recognition = null;
    let cameraStream = null;
    let bootSequenceComplete = false;

    // DOM elements
    const agentCard = document.getElementById('agent-card');
    const bootOverlay = document.querySelector('.boot-overlay');
    const bootStatus = document.querySelector('.boot-status');
    const bootProgress = document.querySelector('.boot-progress-bar');
    const bootConsole = document.querySelector('.boot-console');
    const messageInput = document.getElementById('message-input');
    const chatArea = document.querySelector('.chat-area');
    const permissionsModal = document.getElementById('permissionsModal');

    // Boot sequence
    async function startBootSequence() {
      const steps = [
        { text: 'Initializing Agent Lee™ System...', progress: 10 },
        { text: 'Loading neural networks...', progress: 25 },
        { text: 'Connecting to backend API...', progress: 40 },
        { text: 'Checking system permissions...', progress: 60 },
        { text: 'Initializing voice recognition...', progress: 80 },
        { text: 'System ready!', progress: 100 }
      ];

      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        bootStatus.textContent = step.text;
        bootProgress.style.width = step.progress + '%';

        // Add console line
        const consoleLine = document.createElement('div');
        consoleLine.className = `console-line ${step.progress === 100 ? 'success' : 'info'}`;
        consoleLine.textContent = `[${new Date().toLocaleTimeString()}] ${step.text}`;
        bootConsole.appendChild(consoleLine);
        bootConsole.scrollTop = bootConsole.scrollHeight;

        await new Promise(resolve => setTimeout(resolve, 800));
      }

      // Check API connection
      const connected = await apiClient.checkConnection();
      if (!connected) {
        const warningLine = document.createElement('div');
        warningLine.className = 'console-line warning';
        warningLine.textContent = '[WARNING] Backend API not available - running in demo mode';
        bootConsole.appendChild(warningLine);
      }

      // Hide boot overlay
      setTimeout(() => {
        bootOverlay.classList.add('hidden');
        bootSequenceComplete = true;
        initializeInterface();
      }, 1000);
    }

    // Initialize interface
    function initializeInterface() {
      // Show agent card
      agentCard.style.display = 'block';

      // Add Agent Lee's proper welcome message with IMMEDIATE SPEECH
      setTimeout(() => {
        const welcomeMsg = '🎉 Hello! I\'m Agent Lee™, your AI assistant!';
        addMessage('agent', welcomeMsg);

        // SPEAK IMMEDIATELY
        speakMessage(welcomeMsg);

        setTimeout(() => {
          const capabilitiesMsg = '🚀 I can actually control your system - open apps, search the web, manage your email, and much more!';
          addMessage('agent', capabilitiesMsg);
          speakMessage(capabilitiesMsg);

          setTimeout(() => {
            const instructionsMsg = '💬 Try asking me to "open calculator", "search for something", or just chat with me. Let\'s get started! 😊';
            addMessage('agent', instructionsMsg);
            speakMessage(instructionsMsg);
          }, 3000);
        }, 2500);
      }, 500);

      // Initialize voice recognition if available
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        initializeVoiceRecognition();
      }
    }

    // Enhanced speech function that works immediately
    async function speakMessage(text) {
      try {
        // Try backend TTS first
        const response = await fetch('http://localhost:8000/api/speak', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text: text })
        });

        if (response.ok) {
          console.log('Backend TTS successful');
          return;
        }
      } catch (error) {
        console.log('Backend TTS failed, using browser TTS:', error);
      }

      // Fallback to browser TTS
      if ('speechSynthesis' in window) {
        // Wait for voices to load if needed
        await ensureVoicesLoaded();

        // Cancel any ongoing speech
        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = 0.9;
        utterance.pitch = 1.0;
        utterance.volume = 0.8;

        // Try to use a female voice if available
        const voices = speechSynthesis.getVoices();
        const femaleVoice = voices.find(voice =>
          voice.name.toLowerCase().includes('female') ||
          voice.name.toLowerCase().includes('zira') ||
          voice.name.toLowerCase().includes('hazel') ||
          voice.name.toLowerCase().includes('microsoft')
        );

        if (femaleVoice) {
          utterance.voice = femaleVoice;
          console.log('Using voice:', femaleVoice.name);
        }

        utterance.onstart = () => console.log('Speech started');
        utterance.onend = () => console.log('Speech ended');
        utterance.onerror = (e) => console.error('Speech error:', e);

        speechSynthesis.speak(utterance);
        console.log('Browser TTS started with text:', text.substring(0, 50) + '...');
      }
    }

    // Ensure voices are loaded for browser TTS
    function ensureVoicesLoaded() {
      return new Promise((resolve) => {
        const voices = speechSynthesis.getVoices();
        if (voices.length > 0) {
          resolve();
        } else {
          speechSynthesis.addEventListener('voiceschanged', () => {
            resolve();
          }, { once: true });
        }
      });
    }

    // Voice recognition setup
    function initializeVoiceRecognition() {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognition = new SpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';

      recognition.onresult = function(event) {
        const transcript = event.results[0][0].transcript;
        const confidence = event.results[0][0].confidence;

        addMessage('user', transcript);
        processVoiceCommand(transcript, confidence);
      };

      recognition.onerror = function(event) {
        console.error('Speech recognition error:', event.error);
        isListening = false;
        updateListenButton();
      };

      recognition.onend = function() {
        isListening = false;
        updateListenButton();
      };
    }

    // Add message to chat
    function addMessage(sender, text) {
      const chatArea = document.querySelector('.chat-area');
      const emptyChat = chatArea.querySelector('.empty-chat');

      if (emptyChat) {
        emptyChat.remove();
      }

      const message = document.createElement('div');
      message.className = `message ${sender}-message`;
      message.textContent = text;

      chatArea.appendChild(message);
      chatArea.scrollTop = chatArea.scrollHeight;
    }

    // Process voice command
    async function processVoiceCommand(command, confidence) {
      try {
        const response = await apiClient.processVoiceCommand(command, confidence);
        addMessage('agent', response.response || 'Command processed.');

        if (response.action_taken) {
          await apiClient.speak(response.response);
        }
      } catch (error) {
        console.error('Voice command processing failed:', error);
        addMessage('agent', 'I had trouble processing that command. Please try again.');
      }
    }

    // Update listen button state
    function updateListenButton() {
      const listenBtn = document.getElementById('listen-button');
      if (isListening) {
        listenBtn.classList.add('active');
        listenBtn.innerHTML = '<span class="icon">🎤</span> Listening...';
      } else {
        listenBtn.classList.remove('active');
        listenBtn.innerHTML = '<span class="icon">🎤</span> Listen';
      }
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
      // Start boot sequence
      startBootSequence();

      // Send button - Enhanced with system commands
      const sendButton = document.getElementById('send-button');

      if (sendButton) {
        sendButton.addEventListener('click', async function() {
          const message = messageInput.value.trim();

          if (message) {
            addMessage('user', message);
            messageInput.value = '';

            try {
              // Check for system commands first
              if (await handleSystemCommand(message)) {
                return; // System command handled
              }

              // Regular LLM conversation
              const response = await apiClient.sendLLMMessage(message);
              addMessage('agent', response.response);

              // Speak the response using enhanced speech function
              speakMessage(response.response);
            } catch (error) {
              addMessage('agent', 'I apologize, but I\'m having trouble right now. Please try again.');
            }
          }
        });
      }

      // Handle system commands
      async function handleSystemCommand(message) {
        const lowerMessage = message.toLowerCase().trim();

        // App opening commands - FIXED parsing
        if (lowerMessage.includes('open ') || lowerMessage.includes('launch ') || lowerMessage.includes('start ')) {
          // Extract app name properly
          let appName = '';

          if (lowerMessage.startsWith('open ')) {
            appName = lowerMessage.substring(5).trim();
          } else if (lowerMessage.startsWith('launch ')) {
            appName = lowerMessage.substring(7).trim();
          } else if (lowerMessage.startsWith('start ')) {
            appName = lowerMessage.substring(6).trim();
          } else if (lowerMessage.includes('open ')) {
            const openIndex = lowerMessage.indexOf('open ');
            appName = lowerMessage.substring(openIndex + 5).trim();
          }

          // Clean up app name - remove extra words
          appName = appName.replace(/^(the|a|an)\s+/, ''); // Remove articles
          appName = appName.replace(/\s+(app|application|program)$/, ''); // Remove suffixes
          appName = appName.split(' ')[0]; // Take first word only for most apps

          if (appName) {
            try {
              addMessage('agent', `🚀 Opening ${appName}...`);
              const response = await fetch('http://localhost:8000/api/system/open_app', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ app_name: appName })
              });
              const result = await response.json();
              addMessage('agent', result.message || `✅ ${appName} should be opening now!`);
              return true;
            } catch (error) {
              addMessage('agent', `❌ Had trouble opening ${appName}. Let me try a different approach...`);
              return false;
            }
          }
        }

        // Web search commands - FIXED parsing
        if (lowerMessage.includes('search') || lowerMessage.includes('google') || lowerMessage.includes('find')) {
          let query = '';

          if (lowerMessage.includes('search for ')) {
            query = lowerMessage.substring(lowerMessage.indexOf('search for ') + 11).trim();
          } else if (lowerMessage.includes('google ')) {
            query = lowerMessage.substring(lowerMessage.indexOf('google ') + 7).trim();
          } else if (lowerMessage.includes('search ')) {
            query = lowerMessage.substring(lowerMessage.indexOf('search ') + 7).trim();
          } else if (lowerMessage.includes('find ')) {
            query = lowerMessage.substring(lowerMessage.indexOf('find ') + 5).trim();
          }

          if (query) {
            try {
              addMessage('agent', `🌐 Searching for "${query}"...`);
              const response = await fetch('http://localhost:8000/api/system/web_search', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query: query, engine: 'google' })
              });
              const result = await response.json();
              addMessage('agent', result.message || `✅ Search results should be opening in your browser!`);
              return true;
            } catch (error) {
              addMessage('agent', `❌ Had trouble with the search. Let me try opening it directly...`);
              window.open(`https://www.google.com/search?q=${encodeURIComponent(query)}`, '_blank');
              return true;
            }
          }
        }

        return false; // Not a system command
      }

      // Stop button - stops current speech
      document.getElementById('stop-button').addEventListener('click', function() {
        if ('speechSynthesis' in window) {
          speechSynthesis.cancel();
          addMessage('agent', 'Speech stopped.');
        }
        if (isListening && recognition) {
          recognition.stop();
        }
      });

      // Finish button - continues from where stopped
      document.getElementById('finish-button').addEventListener('click', function() {
        addMessage('agent', 'Continuing from where I left off...');
        // Add logic to continue previous response if needed
      });

      // Email button - REAL functionality
      const emailButton = document.getElementById('email-button');

      if (emailButton) {
        emailButton.addEventListener('click', async function() {
          addMessage('agent', '📧 Opening your email client...');
          try {
            const response = await fetch('http://localhost:8000/api/system/open_email', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({})
            });
            const result = await response.json();
            if (result.status === 'success') {
              addMessage('agent', result.message);
            }
          } catch (error) {
            // Fallback to default email
            window.open('mailto:', '_blank');
          }
        });
      }

      // Phone button - REAL functionality
      const phoneButton = document.getElementById('phone-button');

      if (phoneButton) {
        phoneButton.addEventListener('click', async function() {
          addMessage('agent', '📱 Opening Telegram...');
          try {
            const response = await fetch('http://localhost:8000/api/system/open_messaging', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ app: 'telegram' })
            });
            const result = await response.json();
            if (result.status === 'success') {
              addMessage('agent', result.message);
            }
          } catch (error) {
            addMessage('agent', '📱 Trying to open messaging apps...');
          }
        });
      }

      // Listen button
      document.getElementById('listen-button').addEventListener('click', function() {
        if (!recognition) {
          alert('Voice recognition is not available in your browser.');
          return;
        }

        if (isListening) {
          recognition.stop();
        } else {
          isListening = true;
          updateListenButton();
          recognition.start();
        }
      });

      // Navigation buttons
      document.querySelectorAll('.nav-button').forEach(button => {
        button.addEventListener('click', function() {
          const dashboard = this.getAttribute('data-dashboard');
          const action = this.textContent.trim();

          addMessage('agent', `Opening ${action}...`);

          // Navigate to specific frontend files
          switch(dashboard) {
            case 'todo':
              window.open('frontend/Agent Lee\'s Dynamic To-Do List.html', '_blank');
              break;
            case 'agents':
              window.open('frontend/Agent Lee\'s Agent Center.html', '_blank');
              break;
            case 'database':
              window.open('frontend/AgentLee\'sDB.html', '_blank');
              break;
            case 'llm':
              window.open('frontend/LLM BRAIN CENTER.html', '_blank');
              break;
            case 'workers':
              window.open('frontend/Agent Lee\'s Integrated Workers Center.html', '_blank');
              break;
            default:
              addMessage('agent', `${action} interface coming soon...`);
          }
        });
      });

      // Enter key in message input
      messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          document.getElementById('send-button').click();
        }
      });

      // Camera button
      document.getElementById('camera-button').addEventListener('click', async function() {
        const cameraContainer = document.getElementById('cameraContainer');
        const cameraFeed = document.getElementById('camera-feed');

        if (cameraStream) {
          // Stop camera
          cameraStream.getTracks().forEach(track => track.stop());
          cameraStream = null;
          cameraContainer.classList.remove('active');
          this.innerHTML = '<span class="icon">📷</span> Camera';
        } else {
          // Start camera
          try {
            cameraStream = await navigator.mediaDevices.getUserMedia({ video: true });
            cameraFeed.srcObject = cameraStream;
            cameraContainer.classList.add('active');
            this.innerHTML = '<span class="icon">📷</span> Stop';
          } catch (error) {
            console.error('Camera access failed:', error);
            alert('Camera access denied or not available.');
          }
        }
      });

      // Personality controls
      document.getElementById('personality-select').addEventListener('change', async function() {
        const scene = this.value;
        try {
          const response = await fetch('/api/personality/change_scene', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ scene: scene })
          });

          const result = await response.json();
          if (result.status === 'success') {
            addMessage('agent', `🎭 Personality changed to ${scene} mode! ${getPersonalityMessage(scene)}`);
            updatePersonalityStatus();
          } else {
            addMessage('agent', `Failed to change personality: ${result.message}`);
          }
        } catch (error) {
          console.error('Personality change failed:', error);
          addMessage('agent', 'Sorry, I had trouble changing my personality mode.');
        }
      });

      // Memory button
      document.getElementById('memory-button').addEventListener('click', async function() {
        try {
          const response = await fetch('/api/personality/memory');
          const result = await response.json();

          if (result.status === 'success') {
            const memoryCount = result.memory_size;
            addMessage('agent', `🧠 I remember our last ${memoryCount} conversation exchanges. My memory helps me understand context and maintain our relationship!`);
          }
        } catch (error) {
          console.error('Memory fetch failed:', error);
          addMessage('agent', 'I had trouble accessing my memory right now.');
        }
      });

      // Load initial personality status
      updatePersonalityStatus();

      // Window Controls for Floating Widget
      const minimizeBtn = document.getElementById('minimizeBtn');
      const closeBtn = document.getElementById('closeBtn');
      const minimizedBubble = document.getElementById('minimizedBubble');
      const agentCard = document.getElementById('agent-card');

      // Minimize functionality
      if (minimizeBtn) {
        minimizeBtn.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();

          console.log('Minimizing Agent Lee...');

          // Hide main interface elements
          if (agentCard) agentCard.style.display = 'none';

          const bootOverlay = document.querySelector('.boot-overlay');
          if (bootOverlay) bootOverlay.style.display = 'none';

          const windowControls = document.querySelector('.window-controls');
          if (windowControls) windowControls.style.display = 'none';

          // Show minimized bubble
          if (minimizedBubble) {
            minimizedBubble.style.display = 'flex';
            console.log('Bubble should be visible now');
          }

          // Make bubble draggable
          makeBubbleDraggable();
        });
      }

      // Restore from minimized
      if (minimizedBubble) {
        minimizedBubble.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();

          console.log('Restoring Agent Lee...');

          // Hide bubble
          minimizedBubble.style.display = 'none';

          // Show main interface
          if (agentCard) {
            agentCard.style.display = 'block';
            console.log('Agent card restored');
          }

          const windowControls = document.querySelector('.window-controls');
          if (windowControls) {
            windowControls.style.display = 'flex';
            console.log('Window controls restored');
          }
        });
      }

      // Close functionality
      if (closeBtn) {
        closeBtn.addEventListener('click', function() {
          if (window.electronAPI) {
            window.electronAPI.closeApp();
          } else {
            window.close();
          }
        });
      }

      // Make bubble draggable
      function makeBubbleDraggable() {
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        minimizedBubble.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);

        function dragStart(e) {
          if (e.target === minimizedBubble || minimizedBubble.contains(e.target)) {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;
            isDragging = true;
          }
        }

        function drag(e) {
          if (isDragging) {
            e.preventDefault();
            currentX = e.clientX - initialX;
            currentY = e.clientY - initialY;

            xOffset = currentX;
            yOffset = currentY;

            minimizedBubble.style.transform = `translate3d(${currentX}px, ${currentY}px, 0)`;
          }
        }

        function dragEnd(e) {
          initialX = currentX;
          initialY = currentY;
          isDragging = false;
        }
      }
    });

    // Helper functions for personality
    function getPersonalityMessage(scene) {
      const messages = {
        'default': 'I\'m ready to help with a balanced approach! 😊',
        'funny': 'Time for some laughs! I\'m feeling witty today! 😄',
        'serious': 'I\'m in professional mode. Let\'s get things done efficiently. 💼',
        'excited': 'I\'m super pumped and ready for anything! Let\'s go! 🚀',
        'chill': 'Just taking it easy and going with the flow. What\'s up? 😎'
      };
      return messages[scene] || 'Ready for whatever comes next!';
    }

    async function updatePersonalityStatus() {
      try {
        const response = await fetch('/api/personality/status');
        const result = await response.json();

        if (result.status === 'success') {
          const status = result.personality_status;
          const statusDiv = document.getElementById('personality-status');
          statusDiv.innerHTML = `
            <small>
              Current: ${status.current_scene} |
              Memory: ${status.memory_size} exchanges |
              Humor Level: ${Math.round(status.humor_level * 100)}%
            </small>
          `;
        }
      } catch (error) {
        console.error('Status update failed:', error);
      }
    }

    // Comprehensive Permissions System
    document.addEventListener('DOMContentLoaded', function() {
      // Show permissions modal on startup
      const permissionsModal = document.getElementById('permissionsModal');
      if (permissionsModal) {
        permissionsModal.classList.remove('hidden');
      }

      // Handle permissions granting
      const savePermissionsBtn = document.getElementById('savePermissions');
      if (savePermissionsBtn) {
        savePermissionsBtn.addEventListener('click', async function() {
          await grantAllPermissions();
        });
      }

      const cancelPermissionsBtn = document.getElementById('cancelPermissions');
      if (cancelPermissionsBtn) {
        cancelPermissionsBtn.addEventListener('click', function() {
          // Minimal setup - hide modal and start with basic permissions
          permissionsModal.classList.add('hidden');
          startAgentLee();
        });
      }
    });

    async function grantAllPermissions() {
      console.log('🔐 Granting comprehensive Agent Lee permissions...');

      const permissions = {
        microphone: true,
        camera: true,
        notifications: true,
        storage: true,
        network: true,
        system: true,
        background: true
      };

      // Request actual browser permissions
      await requestBrowserPermissions(permissions);

      // Save to localStorage
      localStorage.setItem('agentlee_permissions', JSON.stringify(permissions));

      // Hide modal
      const permissionsModal = document.getElementById('permissionsModal');
      if (permissionsModal) {
        permissionsModal.classList.add('hidden');
      }

      // Start Agent Lee with full permissions
      await startAgentLeeWithPermissions();
    }

    async function startAgentLeeWithPermissions() {
      console.log('🚀 Starting Agent Lee with full permissions...');

      // Start the main Agent Lee system
      startAgentLee();

      // Show welcome message with permissions granted
      setTimeout(() => {
        const welcomeMessage = "Hello! I'm Agent Lee, your advanced AI assistant. All permissions have been granted and I'm ready to help you with anything you need. I can open applications, search the web, have natural conversations, and much more. What would you like to do first?";
        speakText(welcomeMessage);

        // Update chat with welcome message
        addMessage('agent', welcomeMessage);
      }, 2000);
    }

    async function requestBrowserPermissions(permissions) {
      const results = {};

      try {
        // Request microphone permission
        if (permissions.microphone) {
          try {
            await navigator.mediaDevices.getUserMedia({ audio: true });
            results.microphone = true;
            console.log('✅ Microphone permission granted');
          } catch (error) {
            console.warn('⚠️ Microphone permission denied:', error);
            results.microphone = false;
          }
        }

        // Request camera permission
        if (permissions.camera) {
          try {
            await navigator.mediaDevices.getUserMedia({ video: true });
            results.camera = true;
            console.log('✅ Camera permission granted');
          } catch (error) {
            console.warn('⚠️ Camera permission denied:', error);
            results.camera = false;
          }
        }

        // Request notification permission
        if (permissions.notifications) {
          try {
            const permission = await Notification.requestPermission();
            results.notifications = permission === 'granted';
            console.log('✅ Notification permission:', permission);
          } catch (error) {
            console.warn('⚠️ Notification permission error:', error);
            results.notifications = false;
          }
        }

        // Storage permission (always available)
        if (permissions.storage) {
          results.storage = true;
          console.log('✅ Storage permission granted');
        }

        // Network permission (always available in browser)
        if (permissions.network) {
          results.network = true;
          console.log('✅ Network permission granted');
        }

        // System control (limited in browser)
        if (permissions.system) {
          results.system = true;
          console.log('✅ System control permission granted (limited)');
        }

        // Background processing
        if (permissions.background) {
          results.background = true;
          console.log('✅ Background processing permission granted');
        }

      } catch (error) {
        console.error('Error requesting permissions:', error);
      }

      return results;
    }
  </script>

  <!-- Advanced Agent Lee Systems -->
  <script src="js/agentic-optimization.js"></script>
  <script src="js/optimization-dashboard.js"></script>
  <script src="js/advanced-conversation.js"></script>

  <script>
    // Initialize Advanced Systems
    let agenticOptimization;
    let optimizationDashboard;
    let advancedConversation;

    // Initialize systems after page load
    document.addEventListener('DOMContentLoaded', () => {
      // Initialize Agentic Optimization System
      agenticOptimization = new AgenticOptimizationSystem();

      // Initialize Optimization Dashboard
      optimizationDashboard = agenticOptimization.createDashboard();

      // Initialize Advanced Conversation System
      advancedConversation = new AdvancedConversationSystem();

      console.log('🚀 All Agent Lee advanced systems initialized');
    });

    // Global access for debugging
    window.agentLeeAdvanced = {
      optimization: () => agenticOptimization,
      dashboard: () => optimizationDashboard,
      conversation: () => advancedConversation
    };
  </script>
</body>
</html>