/**
 * 📊 Visual Diagnostics UI - Real-time System Monitoring
 * Charts, Load Bars, Agent Health LEDs, Performance Graphs
 */

export class DiagnosticsUI {
  constructor(containerId = 'diagnostics-container') {
    this.container = document.getElementById(containerId) || this.createContainer();
    this.charts = new Map();
    this.updateInterval = null;
    this.isVisible = false;
    
    this.init();
  }

  createContainer() {
    const container = document.createElement('div');
    container.id = 'diagnostics-container';
    container.className = 'diagnostics-ui';
    document.body.appendChild(container);
    return container;
  }

  init() {
    this.createUI();
    this.addStyles();
    this.bindEvents();
  }

  createUI() {
    this.container.innerHTML = `
      <div class="diagnostics-header">
        <h3>🧠 Agentic Optimization Diagnostics</h3>
        <div class="diagnostics-controls">
          <button id="toggle-diagnostics" class="diag-btn">Toggle</button>
          <button id="export-diagnostics" class="diag-btn">Export</button>
          <button id="reset-diagnostics" class="diag-btn">Reset</button>
        </div>
      </div>

      <div class="diagnostics-content">
        <!-- Real-time Metrics -->
        <div class="metrics-section">
          <div class="metric-card">
            <h4>🎯 Optimization Score</h4>
            <div class="score-display">
              <div class="score-circle" id="optimization-score">
                <span class="score-value">100</span>
                <span class="score-label">%</span>
              </div>
            </div>
          </div>

          <div class="metric-card">
            <h4>⚡ Latency</h4>
            <div class="latency-display">
              <span class="latency-value" id="latency-value">0.000%</span>
              <div class="latency-bar">
                <div class="latency-fill" id="latency-fill"></div>
              </div>
              <small>Target: ≤ 0.1%</small>
            </div>
          </div>

          <div class="metric-card">
            <h4>🧠 Memory</h4>
            <div class="memory-display">
              <span class="memory-value" id="memory-value">0%</span>
              <div class="memory-bar">
                <div class="memory-fill" id="memory-fill"></div>
              </div>
              <small id="memory-details">0 MB / 0 MB</small>
            </div>
          </div>
        </div>

        <!-- Agent Status Grid -->
        <div class="agents-section">
          <h4>🤖 Agent Status (125 max)</h4>
          <div class="agent-stats">
            <div class="stat-item">
              <span class="stat-label">Active:</span>
              <span class="stat-value" id="agents-active">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Suspended:</span>
              <span class="stat-value" id="agents-suspended">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Failed:</span>
              <span class="stat-value" id="agents-failed">0</span>
            </div>
          </div>
          <div class="agent-grid" id="agent-grid">
            <!-- Agent LEDs will be populated here -->
          </div>
        </div>

        <!-- Worker Status -->
        <div class="workers-section">
          <h4>👷 Worker Status</h4>
          <div class="worker-types">
            <div class="worker-type">
              <h5>Web Workers (200 max)</h5>
              <div class="worker-bar">
                <div class="worker-fill" id="web-workers-fill"></div>
                <span class="worker-count" id="web-workers-count">0/200</span>
              </div>
            </div>
            <div class="worker-type">
              <h5>Service Workers (40 max)</h5>
              <div class="worker-bar">
                <div class="worker-fill" id="service-workers-fill"></div>
                <span class="worker-count" id="service-workers-count">0/40</span>
              </div>
            </div>
            <div class="worker-type">
              <h5>Supervisor Workers (15 max)</h5>
              <div class="worker-bar">
                <div class="worker-fill" id="supervisor-workers-fill"></div>
                <span class="worker-count" id="supervisor-workers-count">0/15</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Charts -->
        <div class="charts-section">
          <div class="chart-container">
            <h4>📈 Performance Trends</h4>
            <canvas id="performance-chart" width="400" height="200"></canvas>
          </div>
          <div class="chart-container">
            <h4>🧠 Memory Usage</h4>
            <canvas id="memory-chart" width="400" height="200"></canvas>
          </div>
        </div>

        <!-- System Information -->
        <div class="system-section">
          <h4>💻 System Information</h4>
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">CPU Threads:</span>
              <span class="info-value" id="cpu-threads">-</span>
            </div>
            <div class="info-item">
              <span class="info-label">Device Memory:</span>
              <span class="info-value" id="device-memory">-</span>
            </div>
            <div class="info-item">
              <span class="info-label">Connection:</span>
              <span class="info-value" id="connection-type">-</span>
            </div>
            <div class="info-item">
              <span class="info-label">Uptime:</span>
              <span class="info-value" id="uptime">-</span>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  addStyles() {
    const styles = `
      <style>
        .diagnostics-ui {
          position: fixed;
          top: 20px;
          right: 20px;
          width: 500px;
          max-height: 80vh;
          background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
          border: 2px solid #334155;
          border-radius: 12px;
          color: white;
          font-family: 'Segoe UI', monospace, sans-serif;
          font-size: 12px;
          z-index: 10000;
          overflow-y: auto;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
          transform: translateX(100%);
          transition: transform 0.3s ease;
        }

        .diagnostics-ui.visible {
          transform: translateX(0);
        }

        .diagnostics-header {
          padding: 15px 20px;
          border-bottom: 1px solid #334155;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .diagnostics-header h3 {
          margin: 0;
          font-size: 16px;
          color: #00f2ff;
        }

        .diagnostics-controls {
          display: flex;
          gap: 8px;
        }

        .diag-btn {
          background: #334155;
          border: 1px solid #475569;
          color: white;
          padding: 6px 12px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 11px;
          transition: all 0.2s ease;
        }

        .diag-btn:hover {
          background: #475569;
          border-color: #64748b;
        }

        .diagnostics-content {
          padding: 20px;
        }

        .metrics-section {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 15px;
          margin-bottom: 20px;
        }

        .metric-card {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          padding: 15px;
          border: 1px solid #334155;
        }

        .metric-card h4 {
          margin: 0 0 10px 0;
          font-size: 14px;
          color: #00f2ff;
        }

        .score-display {
          text-align: center;
        }

        .score-circle {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          border: 4px solid #00ff88;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
          position: relative;
        }

        .score-value {
          font-size: 24px;
          font-weight: bold;
          color: #00ff88;
        }

        .score-label {
          font-size: 12px;
          color: #94a3b8;
        }

        .latency-display, .memory-display {
          text-align: center;
        }

        .latency-value, .memory-value {
          font-size: 18px;
          font-weight: bold;
          color: #00ff88;
          display: block;
          margin-bottom: 8px;
        }

        .latency-bar, .memory-bar, .worker-bar {
          width: 100%;
          height: 8px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 5px;
          position: relative;
        }

        .latency-fill, .memory-fill, .worker-fill {
          height: 100%;
          background: linear-gradient(90deg, #00ff88, #00f2ff);
          border-radius: 4px;
          transition: width 0.3s ease;
          width: 0%;
        }

        .agents-section, .workers-section, .charts-section, .system-section {
          margin-bottom: 20px;
        }

        .agents-section h4, .workers-section h4, .charts-section h4, .system-section h4 {
          margin: 0 0 10px 0;
          font-size: 14px;
          color: #00f2ff;
        }

        .agent-stats {
          display: flex;
          gap: 15px;
          margin-bottom: 10px;
        }

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .stat-label {
          font-size: 11px;
          color: #94a3b8;
        }

        .stat-value {
          font-size: 16px;
          font-weight: bold;
          color: #00ff88;
        }

        .agent-grid {
          display: grid;
          grid-template-columns: repeat(25, 1fr);
          gap: 2px;
          margin-top: 10px;
        }

        .agent-led {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #334155;
          transition: background 0.2s ease;
        }

        .agent-led.active { background: #00ff88; }
        .agent-led.suspended { background: #fbbf24; }
        .agent-led.failed { background: #ef4444; }

        .worker-types {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .worker-type h5 {
          margin: 0 0 5px 0;
          font-size: 12px;
          color: #e2e8f0;
        }

        .worker-count {
          position: absolute;
          right: 5px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 10px;
          color: white;
          font-weight: bold;
        }

        .charts-section {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px;
        }

        .chart-container {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          padding: 15px;
          border: 1px solid #334155;
        }

        .chart-container h4 {
          margin: 0 0 10px 0;
          font-size: 12px;
          color: #00f2ff;
        }

        .chart-container canvas {
          width: 100%;
          height: 120px;
          background: rgba(0, 0, 0, 0.2);
          border-radius: 4px;
        }

        .system-info {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10px;
        }

        .info-item {
          display: flex;
          justify-content: space-between;
          padding: 5px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .info-label {
          color: #94a3b8;
          font-size: 11px;
        }

        .info-value {
          color: #e2e8f0;
          font-size: 11px;
          font-weight: bold;
        }
      </style>
    `;
    
    document.head.insertAdjacentHTML('beforeend', styles);
  }

  bindEvents() {
    document.getElementById('toggle-diagnostics')?.addEventListener('click', () => {
      this.toggle();
    });

    document.getElementById('export-diagnostics')?.addEventListener('click', () => {
      this.exportData();
    });

    document.getElementById('reset-diagnostics')?.addEventListener('click', () => {
      this.reset();
    });
  }

  update(snapshot) {
    if (!this.isVisible) return;

    // Update optimization score
    const scoreElement = document.getElementById('optimization-score');
    const scoreValue = Math.round(snapshot.performance.optimizationScore);
    if (scoreElement) {
      scoreElement.querySelector('.score-value').textContent = scoreValue;
      
      // Update color based on score
      const circle = scoreElement;
      if (scoreValue >= 90) circle.style.borderColor = '#00ff88';
      else if (scoreValue >= 70) circle.style.borderColor = '#fbbf24';
      else circle.style.borderColor = '#ef4444';
    }

    // Update latency
    const latencyValue = (snapshot.performance.latency * 100).toFixed(3);
    document.getElementById('latency-value').textContent = `${latencyValue}%`;
    const latencyFill = document.getElementById('latency-fill');
    const latencyPercent = Math.min(100, (snapshot.performance.latency / 0.002) * 100);
    latencyFill.style.width = `${latencyPercent}%`;
    
    // Color based on threshold
    if (snapshot.performance.latency <= 0.001) {
      latencyFill.style.background = 'linear-gradient(90deg, #00ff88, #00f2ff)';
    } else {
      latencyFill.style.background = 'linear-gradient(90deg, #fbbf24, #ef4444)';
    }

    // Update memory
    const memoryPercent = Math.round(snapshot.memory.pressure * 100);
    document.getElementById('memory-value').textContent = `${memoryPercent}%`;
    document.getElementById('memory-fill').style.width = `${memoryPercent}%`;
    document.getElementById('memory-details').textContent = 
      `${Math.round(snapshot.memory.allocated / 1024 / 1024)} MB / ${Math.round(snapshot.memory.total / 1024 / 1024)} MB`;

    // Update agent stats
    document.getElementById('agents-active').textContent = snapshot.agents.active;
    document.getElementById('agents-suspended').textContent = snapshot.agents.suspended;
    document.getElementById('agents-failed').textContent = snapshot.agents.failed;

    // Update agent LEDs
    this.updateAgentLEDs(snapshot.agents);

    // Update worker stats
    this.updateWorkerBars(snapshot.workers);

    // Update system info
    document.getElementById('cpu-threads').textContent = snapshot.system.cpuThreads;
    document.getElementById('device-memory').textContent = snapshot.system.deviceMemory + ' GB';
    document.getElementById('connection-type').textContent = snapshot.system.connectionType;
    document.getElementById('uptime').textContent = this.formatUptime(Date.now() - this.startTime);

    // Update charts
    this.updateCharts(snapshot);
  }

  updateAgentLEDs(agentStats) {
    const grid = document.getElementById('agent-grid');
    const totalAgents = 125; // Max agents
    
    // Clear existing LEDs
    grid.innerHTML = '';
    
    // Create LEDs for all agent slots
    for (let i = 0; i < totalAgents; i++) {
      const led = document.createElement('div');
      led.className = 'agent-led';
      
      if (i < agentStats.active) {
        led.classList.add('active');
      } else if (i < agentStats.active + agentStats.suspended) {
        led.classList.add('suspended');
      } else if (i < agentStats.active + agentStats.suspended + agentStats.failed) {
        led.classList.add('failed');
      }
      
      grid.appendChild(led);
    }
  }

  updateWorkerBars(workerStats) {
    // Web workers
    const webPercent = (workerStats.web / 200) * 100;
    document.getElementById('web-workers-fill').style.width = `${webPercent}%`;
    document.getElementById('web-workers-count').textContent = `${workerStats.web}/200`;

    // Service workers
    const servicePercent = (workerStats.service / 40) * 100;
    document.getElementById('service-workers-fill').style.width = `${servicePercent}%`;
    document.getElementById('service-workers-count').textContent = `${workerStats.service}/40`;

    // Supervisor workers
    const supervisorPercent = (workerStats.supervisor / 15) * 100;
    document.getElementById('supervisor-workers-fill').style.width = `${supervisorPercent}%`;
    document.getElementById('supervisor-workers-count').textContent = `${workerStats.supervisor}/15`;
  }

  updateCharts(snapshot) {
    // Simple canvas-based charts would go here
    // For now, we'll use basic implementations
    this.drawPerformanceChart(snapshot);
    this.drawMemoryChart(snapshot);
  }

  drawPerformanceChart(snapshot) {
    const canvas = document.getElementById('performance-chart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Draw simple performance trend
    ctx.strokeStyle = '#00f2ff';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(0, height / 2);
    ctx.lineTo(width, height / 2 - (snapshot.performance.optimizationScore - 50));
    ctx.stroke();
  }

  drawMemoryChart(snapshot) {
    const canvas = document.getElementById('memory-chart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Draw memory usage
    const memoryHeight = (snapshot.memory.pressure * height);
    ctx.fillStyle = '#00ff88';
    ctx.fillRect(0, height - memoryHeight, width, memoryHeight);
  }

  formatUptime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }

  show() {
    this.isVisible = true;
    this.container.classList.add('visible');
    this.startTime = Date.now();
    
    // Start update loop
    this.updateInterval = setInterval(() => {
      if (window.OptimizationEngine) {
        const snapshot = window.OptimizationEngine.getSnapshot();
        this.update(snapshot);
      }
    }, 1000);
  }

  hide() {
    this.isVisible = false;
    this.container.classList.remove('visible');
    
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  exportData() {
    if (window.OptimizationEngine) {
      const data = window.OptimizationEngine.getMetrics();
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `diagnostics-${Date.now()}.json`;
      a.click();
      
      URL.revokeObjectURL(url);
    }
  }

  reset() {
    if (window.OptimizationEngine) {
      // Reset would clear metrics history
      console.log('[DiagnosticsUI] Reset requested');
    }
  }
}

// Auto-initialize if in browser
if (typeof window !== 'undefined') {
  window.DiagnosticsUI = DiagnosticsUI;
}
