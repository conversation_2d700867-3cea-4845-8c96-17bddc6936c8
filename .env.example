# Agent Lee™ System Configuration
# Copy this file to .env and fill in your actual values

# API Configuration
AGENTLEE_DEBUG=False
AGENTLEE_HOST=127.0.0.1
AGENTLEE_PORT=8000

# Security Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000,http://127.0.0.1:8000

# Google Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///./agentlee.db
# For PostgreSQL: postgresql://user:password@localhost/agentlee

# Ollama Configuration (for local LLM fallback)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=agentlee.log

# TTS Configuration
TTS_ENGINE=pyttsx3
TTS_VOICE=default
TTS_RATE=1.0

# Voice Recognition Configuration
VOICE_RECOGNITION_TIMEOUT=5
VOICE_RECOGNITION_PHRASE_TIMEOUT=1
