# Agent Lee™ System Environment Configuration
# Copy this file to .env and configure your settings

# ===== SERVER CONFIGURATION =====
AGENTLEE_HOST=127.0.0.1
AGENTLEE_PORT=8000
AGENTLEE_DEBUG=False

# ===== SECURITY CONFIGURATION =====
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000,http://127.0.0.1:8000,file://

# ===== AI & LLM API KEYS =====
# Get your Gemini API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: OpenAI API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Anthropic API key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ===== DATABASE CONFIGURATION =====
DATABASE_URL=sqlite:///./agentlee.db

# ===== OLLAMA CONFIGURATION (Local LLM) =====
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# ===== LOGGING CONFIGURATION =====
LOG_LEVEL=INFO
LOG_FILE=agentlee.log

# ===== TEXT-TO-SPEECH CONFIGURATION =====
TTS_ENGINE=pyttsx3
TTS_VOICE=default
TTS_RATE=1.0

# ===== VOICE RECOGNITION CONFIGURATION =====
VOICE_RECOGNITION_TIMEOUT=5
VOICE_RECOGNITION_PHRASE_TIMEOUT=1

# ===== BROWSER CONFIGURATION =====
# Paths to browsers (auto-detected if not specified)
CHROME_PATH=
FIREFOX_PATH=

# ===== EMAIL CONFIGURATION (Optional) =====
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password

# ===== ADVANCED FEATURES =====
# Enable experimental features
ENABLE_VOICE_COMMANDS=true
ENABLE_SYSTEM_CONTROL=true
ENABLE_WEB_AUTOMATION=false

# ===== DEVELOPMENT SETTINGS =====
# Set to true for development mode
DEVELOPMENT_MODE=false
ENABLE_API_DOCS=true
