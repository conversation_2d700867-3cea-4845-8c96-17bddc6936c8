"""
Agent Lee™ System Database Models
SQLAlchemy models for Agent Lee data persistence
"""

from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# Database setup
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./agentlee.db")
engine = create_engine(DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class Conversation(Base):
    """Conversation history model"""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_message = Column(Text, nullable=False)
    agent_response = Column(Text, nullable=False)
    personality_mode = Column(String(50), default="default")
    timestamp = Column(DateTime, default=datetime.utcnow)
    session_id = Column(String(100), index=True)

class SystemAction(Base):
    """System actions log model"""
    __tablename__ = "system_actions"
    
    id = Column(Integer, primary_key=True, index=True)
    action_type = Column(String(100), nullable=False)  # "open_app", "search_web", etc.
    action_data = Column(Text)  # JSON data
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow)

class UserPreference(Base):
    """User preferences model"""
    __tablename__ = "user_preferences"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, nullable=False)
    value = Column(Text, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow)

class AgentMemory(Base):
    """Agent memory and context model"""
    __tablename__ = "agent_memory"
    
    id = Column(Integer, primary_key=True, index=True)
    memory_type = Column(String(50), nullable=False)  # "fact", "preference", "context"
    content = Column(Text, nullable=False)
    importance = Column(Float, default=1.0)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_accessed = Column(DateTime, default=datetime.utcnow)

class HealthMetric(Base):
    """System health metrics model"""
    __tablename__ = "health_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    unit = Column(String(20), nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow)

def create_tables():
    """Create all database tables"""
    try:
        Base.metadata.create_all(bind=engine)
        return True
    except Exception as e:
        print(f"Error creating tables: {e}")
        return False

def get_db():
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Initialize database on import
if __name__ == "__main__":
    create_tables()
    print("Database tables created successfully")
