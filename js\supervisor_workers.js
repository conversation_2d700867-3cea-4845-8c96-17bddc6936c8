/**
 * 👷 Supervisor Workers - Specialized Optimization Agents
 * PruneAgent, ThrottleAgent, BalanceAgent, StreamAgent, MonitorAgent
 */

// 🗑️ PruneAgent - Removes dead loops and memory leaks
class PruneAgent {
  constructor() {
    this.name = 'PruneAgent';
    this.priority = 'high';
    this.status = 'active';
    this.lastPrune = Date.now();
    this.pruneInterval = 30000; // 30 seconds
    this.deadLoops = new Set();
    this.memoryLeaks = new Map();
  }

  async optimize(metrics) {
    const now = Date.now();
    if (now - this.lastPrune < this.pruneInterval) return;

    console.log('[PruneAgent] Starting optimization cycle');
    
    // Detect and remove dead loops
    await this.pruneDeadLoops();
    
    // Clean up memory leaks
    await this.pruneMemoryLeaks(metrics);
    
    // Clean up orphaned event listeners
    await this.pruneEventListeners();
    
    // Clean up unused DOM elements
    await this.pruneDOMElements();
    
    this.lastPrune = now;
    this.updateHeartbeat();
  }

  async pruneDeadLoops() {
    // Detect infinite loops or stuck processes
    const suspiciousIntervals = [];
    
    // Check for intervals that haven't updated their lastRun
    if (window.activeIntervals) {
      window.activeIntervals.forEach((interval, id) => {
        if (Date.now() - interval.lastRun > 60000) { // 1 minute stuck
          suspiciousIntervals.push(id);
        }
      });
    }

    // Clear suspicious intervals
    suspiciousIntervals.forEach(id => {
      clearInterval(id);
      this.deadLoops.add(id);
      console.log(`[PruneAgent] Cleared dead loop: ${id}`);
    });

    return suspiciousIntervals.length;
  }

  async pruneMemoryLeaks(metrics) {
    // Detect memory leaks by tracking object growth
    const currentMemory = metrics.memory.allocated;
    const memoryGrowth = currentMemory - (this.lastMemoryCheck || currentMemory);
    
    if (memoryGrowth > 10 * 1024 * 1024) { // 10MB growth
      console.warn('[PruneAgent] Potential memory leak detected');
      
      // Force garbage collection
      if (window.gc) {
        window.gc();
      }
      
      // Clear large caches
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        for (const name of cacheNames) {
          if (name.includes('temp') || name.includes('old')) {
            await caches.delete(name);
            console.log(`[PruneAgent] Cleared cache: ${name}`);
          }
        }
      }
    }
    
    this.lastMemoryCheck = currentMemory;
  }

  async pruneEventListeners() {
    // Remove orphaned event listeners
    const elements = document.querySelectorAll('*');
    let removed = 0;
    
    elements.forEach(element => {
      // Check for elements with many listeners but no parent
      if (!element.parentNode && element._listeners) {
        // Remove all listeners
        Object.keys(element._listeners).forEach(event => {
          element.removeEventListener(event, element._listeners[event]);
        });
        removed++;
      }
    });
    
    if (removed > 0) {
      console.log(`[PruneAgent] Removed ${removed} orphaned event listeners`);
    }
  }

  async pruneDOMElements() {
    // Remove unused DOM elements
    const unusedElements = document.querySelectorAll('[data-unused="true"], .temp-element');
    unusedElements.forEach(element => {
      element.remove();
    });
    
    if (unusedElements.length > 0) {
      console.log(`[PruneAgent] Removed ${unusedElements.length} unused DOM elements`);
    }
  }

  updateHeartbeat() {
    this.lastHeartbeat = Date.now();
  }

  suspend() {
    this.status = 'suspended';
    console.log('[PruneAgent] Suspended');
  }

  resume() {
    this.status = 'active';
    console.log('[PruneAgent] Resumed');
  }
}

// ⚡ ThrottleAgent - Manages loop frequency and resource usage
class ThrottleAgent {
  constructor() {
    this.name = 'ThrottleAgent';
    this.priority = 'high';
    this.status = 'active';
    this.throttleRules = new Map();
    this.activeThrottles = new Map();
  }

  async optimize(metrics) {
    console.log('[ThrottleAgent] Analyzing system load');
    
    // Check if throttling is needed
    const needsThrottling = this.assessThrottlingNeed(metrics);
    
    if (needsThrottling.memory) {
      await this.throttleMemoryIntensive();
    }
    
    if (needsThrottling.cpu) {
      await this.throttleCPUIntensive();
    }
    
    if (needsThrottling.network) {
      await this.throttleNetworkRequests();
    }
    
    // Remove throttles if system is healthy
    if (metrics.performance.optimizationScore > 85) {
      await this.removeThrottles();
    }
    
    this.updateHeartbeat();
  }

  assessThrottlingNeed(metrics) {
    return {
      memory: metrics.memory.pressure > 0.8,
      cpu: metrics.performance.latency > 0.002,
      network: metrics.workers.web > 150 // High worker count indicates high network usage
    };
  }

  async throttleMemoryIntensive() {
    // Throttle memory-intensive operations
    const memoryIntensiveSelectors = [
      'canvas',
      'video',
      '[data-memory-intensive]'
    ];
    
    memoryIntensiveSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (element.pause && typeof element.pause === 'function') {
          element.pause();
        }
        if (element.style) {
          element.style.display = 'none';
        }
      });
    });
    
    // Throttle animation frames
    this.throttleAnimationFrames(0.5); // 50% reduction
    
    console.log('[ThrottleAgent] Applied memory throttling');
  }

  async throttleCPUIntensive() {
    // Reduce CPU-intensive operations
    
    // Throttle timers
    this.throttleTimers(2); // Double intervals
    
    // Reduce animation quality
    this.reduceAnimationQuality();
    
    // Pause non-critical workers
    if (window.OptimizationEngine) {
      const workers = window.OptimizationEngine.getSnapshot().workers;
      // Logic to pause low-priority workers would go here
    }
    
    console.log('[ThrottleAgent] Applied CPU throttling');
  }

  async throttleNetworkRequests() {
    // Implement request queuing and rate limiting
    const originalFetch = window.fetch;
    let requestQueue = [];
    let activeRequests = 0;
    const maxConcurrentRequests = 5;
    
    window.fetch = async function(...args) {
      return new Promise((resolve, reject) => {
        requestQueue.push({ args, resolve, reject });
        processQueue();
      });
    };
    
    function processQueue() {
      if (activeRequests >= maxConcurrentRequests || requestQueue.length === 0) {
        return;
      }
      
      const { args, resolve, reject } = requestQueue.shift();
      activeRequests++;
      
      originalFetch(...args)
        .then(resolve)
        .catch(reject)
        .finally(() => {
          activeRequests--;
          processQueue();
        });
    }
    
    this.activeThrottles.set('network', { originalFetch });
    console.log('[ThrottleAgent] Applied network throttling');
  }

  throttleAnimationFrames(factor) {
    if (this.activeThrottles.has('animationFrames')) return;
    
    const originalRAF = window.requestAnimationFrame;
    let frameSkip = 0;
    
    window.requestAnimationFrame = function(callback) {
      frameSkip++;
      if (frameSkip % Math.ceil(1 / factor) === 0) {
        return originalRAF(callback);
      }
      return setTimeout(callback, 16); // Fallback to setTimeout
    };
    
    this.activeThrottles.set('animationFrames', { originalRAF });
  }

  throttleTimers(multiplier) {
    if (this.activeThrottles.has('timers')) return;
    
    const originalSetInterval = window.setInterval;
    const originalSetTimeout = window.setTimeout;
    
    window.setInterval = function(callback, delay) {
      return originalSetInterval(callback, delay * multiplier);
    };
    
    window.setTimeout = function(callback, delay) {
      return originalSetTimeout(callback, delay * multiplier);
    };
    
    this.activeThrottles.set('timers', { originalSetInterval, originalSetTimeout });
  }

  reduceAnimationQuality() {
    // Reduce CSS animation quality
    const style = document.createElement('style');
    style.textContent = `
      * {
        animation-duration: 0.1s !important;
        transition-duration: 0.1s !important;
      }
    `;
    document.head.appendChild(style);
    
    this.activeThrottles.set('animations', { style });
  }

  async removeThrottles() {
    // Restore original functions
    this.activeThrottles.forEach((throttle, type) => {
      switch (type) {
        case 'network':
          window.fetch = throttle.originalFetch;
          break;
        case 'animationFrames':
          window.requestAnimationFrame = throttle.originalRAF;
          break;
        case 'timers':
          window.setInterval = throttle.originalSetInterval;
          window.setTimeout = throttle.originalSetTimeout;
          break;
        case 'animations':
          throttle.style.remove();
          break;
      }
    });
    
    this.activeThrottles.clear();
    console.log('[ThrottleAgent] Removed all throttles');
  }

  updateHeartbeat() {
    this.lastHeartbeat = Date.now();
  }

  suspend() {
    this.status = 'suspended';
  }

  resume() {
    this.status = 'active';
  }
}

// ⚖️ BalanceAgent - Reallocates task loads
class BalanceAgent {
  constructor() {
    this.name = 'BalanceAgent';
    this.priority = 'medium';
    this.status = 'active';
    this.loadHistory = [];
    this.rebalanceThreshold = 0.3; // 30% load difference triggers rebalancing
  }

  async optimize(metrics) {
    console.log('[BalanceAgent] Analyzing load distribution');
    
    // Analyze worker load distribution
    await this.analyzeWorkerLoads();
    
    // Rebalance if needed
    const needsRebalancing = this.assessRebalancingNeed();
    
    if (needsRebalancing) {
      await this.rebalanceWorkers();
      await this.rebalanceAgents();
    }
    
    // Update load history
    this.updateLoadHistory(metrics);
    
    this.updateHeartbeat();
  }

  async analyzeWorkerLoads() {
    // Analyze load across different worker types
    if (window.OptimizationEngine) {
      const snapshot = window.OptimizationEngine.getSnapshot();
      
      this.currentLoads = {
        web: snapshot.workers.web / 200, // Percentage of max
        service: snapshot.workers.service / 40,
        supervisor: snapshot.workers.supervisor / 15
      };
    }
  }

  assessRebalancingNeed() {
    if (!this.currentLoads) return false;
    
    const loads = Object.values(this.currentLoads);
    const maxLoad = Math.max(...loads);
    const minLoad = Math.min(...loads);
    
    return (maxLoad - minLoad) > this.rebalanceThreshold;
  }

  async rebalanceWorkers() {
    // Move tasks from overloaded workers to underloaded ones
    const { web, service, supervisor } = this.currentLoads;
    
    if (web > 0.8 && service < 0.5) {
      // Move some web worker tasks to service workers
      console.log('[BalanceAgent] Rebalancing web workers to service workers');
      // Implementation would depend on specific worker architecture
    }
    
    if (supervisor > 0.8) {
      // Distribute supervisor tasks
      console.log('[BalanceAgent] Redistributing supervisor worker tasks');
    }
  }

  async rebalanceAgents() {
    // Rebalance agent workloads
    if (window.OptimizationEngine) {
      // Get agent information and redistribute tasks
      console.log('[BalanceAgent] Rebalancing agent workloads');
    }
  }

  updateLoadHistory(metrics) {
    this.loadHistory.push({
      timestamp: Date.now(),
      loads: { ...this.currentLoads },
      optimizationScore: metrics.performance.optimizationScore
    });
    
    // Keep only last 100 entries
    if (this.loadHistory.length > 100) {
      this.loadHistory.shift();
    }
  }

  updateHeartbeat() {
    this.lastHeartbeat = Date.now();
  }

  suspend() {
    this.status = 'suspended';
  }

  resume() {
    this.status = 'active';
  }
}

// 🌊 StreamAgent - Monitors real-time I/O traffic
class StreamAgent {
  constructor() {
    this.name = 'StreamAgent';
    this.priority = 'medium';
    this.status = 'active';
    this.streamMetrics = {
      inbound: 0,
      outbound: 0,
      errors: 0,
      latency: []
    };
    this.monitoredStreams = new Map();
  }

  async optimize(metrics) {
    console.log('[StreamAgent] Monitoring I/O streams');
    
    // Monitor network streams
    await this.monitorNetworkStreams();
    
    // Monitor WebSocket connections
    await this.monitorWebSockets();
    
    // Monitor fetch requests
    await this.monitorFetchRequests();
    
    // Optimize streams if needed
    await this.optimizeStreams();
    
    this.updateHeartbeat();
  }

  async monitorNetworkStreams() {
    // Monitor network performance
    if (navigator.connection) {
      const connection = navigator.connection;
      this.streamMetrics.bandwidth = connection.downlink;
      this.streamMetrics.rtt = connection.rtt;
      this.streamMetrics.effectiveType = connection.effectiveType;
    }
  }

  async monitorWebSockets() {
    // Monitor WebSocket connections
    // This would require hooking into WebSocket creation
    const originalWebSocket = window.WebSocket;
    const streamAgent = this;
    
    window.WebSocket = function(url, protocols) {
      const ws = new originalWebSocket(url, protocols);
      const id = Date.now() + Math.random();
      
      streamAgent.monitoredStreams.set(id, {
        type: 'websocket',
        url: url,
        created: Date.now(),
        messageCount: 0,
        errorCount: 0
      });
      
      ws.addEventListener('message', () => {
        const stream = streamAgent.monitoredStreams.get(id);
        if (stream) stream.messageCount++;
      });
      
      ws.addEventListener('error', () => {
        const stream = streamAgent.monitoredStreams.get(id);
        if (stream) stream.errorCount++;
      });
      
      ws.addEventListener('close', () => {
        streamAgent.monitoredStreams.delete(id);
      });
      
      return ws;
    };
  }

  async monitorFetchRequests() {
    // Monitor fetch request performance
    const originalFetch = window.fetch;
    const streamAgent = this;
    
    window.fetch = async function(...args) {
      const startTime = performance.now();
      
      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();
        
        streamAgent.streamMetrics.inbound++;
        streamAgent.streamMetrics.latency.push(endTime - startTime);
        
        // Keep only last 100 latency measurements
        if (streamAgent.streamMetrics.latency.length > 100) {
          streamAgent.streamMetrics.latency.shift();
        }
        
        return response;
      } catch (error) {
        streamAgent.streamMetrics.errors++;
        throw error;
      }
    };
  }

  async optimizeStreams() {
    // Optimize based on stream metrics
    const avgLatency = this.streamMetrics.latency.reduce((a, b) => a + b, 0) / this.streamMetrics.latency.length;
    
    if (avgLatency > 1000) { // High latency
      console.warn('[StreamAgent] High network latency detected, optimizing streams');
      
      // Implement stream optimization strategies
      await this.enableCompression();
      await this.batchRequests();
    }
    
    if (this.streamMetrics.errors > 10) {
      console.warn('[StreamAgent] High error rate detected');
      await this.implementRetryLogic();
    }
  }

  async enableCompression() {
    // Enable compression for requests
    console.log('[StreamAgent] Enabling compression');
  }

  async batchRequests() {
    // Batch multiple requests together
    console.log('[StreamAgent] Implementing request batching');
  }

  async implementRetryLogic() {
    // Implement retry logic for failed requests
    console.log('[StreamAgent] Implementing retry logic');
  }

  updateHeartbeat() {
    this.lastHeartbeat = Date.now();
  }

  suspend() {
    this.status = 'suspended';
  }

  resume() {
    this.status = 'active';
  }
}

// 📊 MonitorAgent - Overall system monitoring
class MonitorAgent {
  constructor() {
    this.name = 'MonitorAgent';
    this.priority = 'critical';
    this.status = 'active';
    this.alerts = [];
    this.thresholds = {
      memoryPressure: 0.9,
      latency: 0.005,
      errorRate: 0.1,
      optimizationScore: 50
    };
  }

  async optimize(metrics) {
    // Monitor all system metrics and generate alerts
    await this.checkThresholds(metrics);
    await this.generateReports();
    await this.triggerAlerts();
    
    this.updateHeartbeat();
  }

  async checkThresholds(metrics) {
    const alerts = [];
    
    if (metrics.memory.pressure > this.thresholds.memoryPressure) {
      alerts.push({
        type: 'critical',
        message: `Critical memory pressure: ${(metrics.memory.pressure * 100).toFixed(1)}%`,
        timestamp: Date.now()
      });
    }
    
    if (metrics.performance.latency > this.thresholds.latency) {
      alerts.push({
        type: 'warning',
        message: `High latency: ${(metrics.performance.latency * 100).toFixed(3)}%`,
        timestamp: Date.now()
      });
    }
    
    if (metrics.performance.optimizationScore < this.thresholds.optimizationScore) {
      alerts.push({
        type: 'warning',
        message: `Low optimization score: ${metrics.performance.optimizationScore}`,
        timestamp: Date.now()
      });
    }
    
    this.alerts.push(...alerts);
    
    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }
  }

  async generateReports() {
    // Generate periodic system reports
    const report = {
      timestamp: Date.now(),
      summary: 'System operating within normal parameters',
      recommendations: []
    };
    
    // Add to reports history
    if (!this.reports) this.reports = [];
    this.reports.push(report);
    
    if (this.reports.length > 50) {
      this.reports.shift();
    }
  }

  async triggerAlerts() {
    // Trigger alerts for critical issues
    const criticalAlerts = this.alerts.filter(alert => 
      alert.type === 'critical' && Date.now() - alert.timestamp < 60000
    );
    
    criticalAlerts.forEach(alert => {
      console.error(`[MonitorAgent] CRITICAL ALERT: ${alert.message}`);
      
      // Could trigger notifications, emails, etc.
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('Agent Lee System Alert', {
          body: alert.message,
          icon: '/favicon.ico'
        });
      }
    });
  }

  updateHeartbeat() {
    this.lastHeartbeat = Date.now();
  }

  suspend() {
    this.status = 'suspended';
  }

  resume() {
    this.status = 'active';
  }
}

// 🏭 Supervisor Worker Factory
export class SupervisorWorkerFactory {
  static createSupervisorWorkers() {
    return {
      pruneAgent: new PruneAgent(),
      throttleAgent: new ThrottleAgent(),
      balanceAgent: new BalanceAgent(),
      streamAgent: new StreamAgent(),
      monitorAgent: new MonitorAgent()
    };
  }

  static registerWithOptimizationEngine(optimizationEngine) {
    const supervisors = this.createSupervisorWorkers();
    
    Object.entries(supervisors).forEach(([name, agent]) => {
      optimizationEngine.registerAgent(name, agent);
      console.log(`[SupervisorFactory] Registered ${name}`);
    });
    
    return supervisors;
  }
}

// Auto-register if OptimizationEngine is available
if (typeof window !== 'undefined' && window.OptimizationEngine) {
  window.SupervisorWorkers = SupervisorWorkerFactory.createSupervisorWorkers();
  console.log('[SupervisorWorkers] Supervisor workers loaded and ready');
}
