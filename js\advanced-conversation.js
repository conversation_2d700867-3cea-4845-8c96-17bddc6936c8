/**
 * 🧠 Advanced Conversational AI System
 * Implements interruption handling, emotional intelligence, and context memory
 */

class AdvancedConversationSystem {
  constructor() {
    this.state = {
      LISTENING: 'listening',
      SPEAKING: 'speaking',
      PROCESSING: 'processing',
      INTERRUPTED: 'interrupted'
    };
    
    this.currentState = this.state.LISTENING;
    this.conversationStack = [];
    this.interruptBuffer = [];
    this.contextMemory = new ConversationMemory();
    this.emotionalState = new EmotionalIntelligence();
    this.speechManager = new InterruptibleSpeechManager();
    
    this.isInitialized = false;
    this.initializeSystem();
  }

  async initializeSystem() {
    try {
      await this.speechManager.initialize();
      await this.contextMemory.initialize();
      this.emotionalState.initialize();
      
      this.isInitialized = true;
      console.log('🧠 Advanced Conversation System initialized');
      
      // Start listening for interruptions
      this.startInterruptionMonitoring();
      
    } catch (error) {
      console.error('Failed to initialize conversation system:', error);
    }
  }

  async processInput(input, context = {}) {
    if (!this.isInitialized) {
      await this.initializeSystem();
    }

    // Handle interruption if currently speaking
    if (this.currentState === this.state.SPEAKING) {
      return this.handleInterruption(input);
    }

    this.currentState = this.state.PROCESSING;
    
    try {
      // Add to conversation memory
      this.contextMemory.addUserInput(input, context);
      
      // Analyze emotional context
      const emotionalContext = this.emotionalState.analyzeInput(input);
      
      // Generate response with full context
      const response = await this.generateContextualResponse(input, emotionalContext);
      
      // Speak the response
      await this.speakResponse(response, emotionalContext);
      
      return response;
      
    } catch (error) {
      console.error('Error processing input:', error);
      return this.generateErrorResponse();
    }
  }

  async handleInterruption(interruptionText) {
    console.log('🔄 Handling interruption:', interruptionText);
    
    // Stop current speech
    this.speechManager.stopSpeaking();
    
    // Save current conversation state
    this.conversationStack.push({
      state: this.currentState,
      context: this.contextMemory.getCurrentContext(),
      timestamp: Date.now()
    });
    
    this.currentState = this.state.INTERRUPTED;
    
    // Analyze interruption intent
    const interruptionIntent = this.analyzeInterruptionIntent(interruptionText);
    
    // Generate appropriate interruption response
    const response = this.generateInterruptionResponse(interruptionIntent);
    
    // Speak interruption acknowledgment
    await this.speakResponse(response, { tone: 'understanding', priority: 'high' });
    
    // Process the interruption as new input
    return this.processInput(interruptionText, { isInterruption: true });
  }

  analyzeInterruptionIntent(text) {
    const intents = {
      clarification: /what|how|why|explain|clarify/i,
      correction: /no|wrong|actually|correct|fix/i,
      addition: /also|and|plus|furthermore|additionally/i,
      urgency: /urgent|important|emergency|now|immediately/i,
      stop: /stop|enough|nevermind|cancel/i
    };

    for (const [intent, pattern] of Object.entries(intents)) {
      if (pattern.test(text)) {
        return intent;
      }
    }

    return 'general';
  }

  generateInterruptionResponse(intent) {
    const responses = {
      clarification: [
        "Let me clarify that for you...",
        "I understand you need more explanation...",
        "Good question, let me break that down..."
      ],
      correction: [
        "Ah, I see what you mean...",
        "You're right, let me correct that...",
        "Thanks for the correction..."
      ],
      addition: [
        "Yes, and there's more to consider...",
        "That's a great addition...",
        "Absolutely, let me add to that..."
      ],
      urgency: [
        "I understand this is urgent...",
        "Let me address this immediately...",
        "This is important, let me help right away..."
      ],
      stop: [
        "Of course, I'll stop there.",
        "Understood, moving on...",
        "No problem, what would you like instead?"
      ],
      general: [
        "I hear you...",
        "Let me address that...",
        "That's a good point..."
      ]
    };

    const responseList = responses[intent] || responses.general;
    return responseList[Math.floor(Math.random() * responseList.length)];
  }

  async generateContextualResponse(input, emotionalContext) {
    // Get conversation history
    const history = this.contextMemory.getRecentHistory(5);
    
    // Build context for AI
    const contextPrompt = this.buildContextPrompt(input, history, emotionalContext);
    
    try {
      // Call backend API for AI response
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: input,
          context: contextPrompt,
          emotional_state: emotionalContext,
          conversation_history: history
        })
      });

      if (!response.ok) {
        throw new Error('API request failed');
      }

      const data = await response.json();
      
      // Add AI response to memory
      this.contextMemory.addAIResponse(data.response, emotionalContext);
      
      return data.response;
      
    } catch (error) {
      console.error('Error generating response:', error);
      return this.generateFallbackResponse(input, emotionalContext);
    }
  }

  buildContextPrompt(input, history, emotionalContext) {
    return `
      [CONVERSATION CONTEXT]
      Recent conversation history: ${JSON.stringify(history)}
      
      [EMOTIONAL CONTEXT]
      User emotion: ${emotionalContext.primaryEmotion}
      Confidence: ${emotionalContext.confidence}
      Tone: ${emotionalContext.tone}
      
      [PERSONALITY SETTINGS]
      - Be conversational and natural
      - Show emotional intelligence
      - Use humor when appropriate
      - Be helpful and proactive
      - Handle interruptions gracefully
      
      [CURRENT INPUT]
      User: ${input}
      
      Respond as Agent Lee with personality and emotional awareness:
    `;
  }

  generateFallbackResponse(input, emotionalContext) {
    const fallbackResponses = [
      "I understand what you're saying. Let me think about that...",
      "That's an interesting point. Can you tell me more?",
      "I hear you. Let me process that information...",
      "Thanks for sharing that with me. How can I help further?"
    ];

    // Adjust response based on emotional context
    if (emotionalContext.primaryEmotion === 'frustrated') {
      return "I can sense you might be frustrated. Let me try to help you better.";
    } else if (emotionalContext.primaryEmotion === 'excited') {
      return "I love your enthusiasm! Tell me more about what's exciting you.";
    }

    return fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];
  }

  async speakResponse(text, emotionalContext) {
    this.currentState = this.state.SPEAKING;
    
    try {
      await this.speechManager.speak(text, emotionalContext);
      this.currentState = this.state.LISTENING;
    } catch (error) {
      console.error('Error speaking response:', error);
      this.currentState = this.state.LISTENING;
    }
  }

  startInterruptionMonitoring() {
    // This would integrate with speech recognition to detect interruptions
    // For now, we'll set up a basic monitoring system
    
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.interruptionRecognition = new SpeechRecognition();
      
      this.interruptionRecognition.continuous = true;
      this.interruptionRecognition.interimResults = true;
      
      this.interruptionRecognition.onresult = (event) => {
        if (this.currentState === this.state.SPEAKING) {
          const transcript = event.results[event.results.length - 1][0].transcript;
          if (transcript.trim().length > 3) { // Avoid false positives
            this.handleInterruption(transcript);
          }
        }
      };
      
      // Start monitoring when speaking
      this.speechManager.onSpeakStart = () => {
        this.interruptionRecognition.start();
      };
      
      this.speechManager.onSpeakEnd = () => {
        this.interruptionRecognition.stop();
      };
    }
  }

  generateErrorResponse() {
    return "I apologize, but I'm having trouble processing that right now. Could you please try again?";
  }

  // Public methods for external control
  getCurrentState() {
    return this.currentState;
  }

  getConversationHistory() {
    return this.contextMemory.getFullHistory();
  }

  clearConversationHistory() {
    this.contextMemory.clear();
    this.conversationStack = [];
  }

  setEmotionalState(emotion, intensity = 0.5) {
    this.emotionalState.setCurrentEmotion(emotion, intensity);
  }

  shutdown() {
    if (this.interruptionRecognition) {
      this.interruptionRecognition.stop();
    }
    
    this.speechManager.shutdown();
    this.contextMemory.shutdown();
    
    console.log('🔄 Advanced Conversation System shutdown');
  }
}

/**
 * 🧠 Conversation Memory - Context and history management
 */
class ConversationMemory {
  constructor() {
    this.history = [];
    this.maxHistoryLength = 100;
    this.contextWindow = 10;
    this.topics = new Map();
    this.userPreferences = new Map();
  }

  async initialize() {
    // Load conversation history from storage
    try {
      const stored = localStorage.getItem('agentlee_conversation_history');
      if (stored) {
        this.history = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Could not load conversation history:', error);
    }
  }

  addUserInput(input, context = {}) {
    const entry = {
      type: 'user',
      content: input,
      timestamp: Date.now(),
      context: context,
      topics: this.extractTopics(input)
    };

    this.history.push(entry);
    this.updateTopics(entry.topics);
    this.pruneHistory();
    this.saveToStorage();
  }

  addAIResponse(response, emotionalContext = {}) {
    const entry = {
      type: 'ai',
      content: response,
      timestamp: Date.now(),
      emotionalContext: emotionalContext,
      topics: this.extractTopics(response)
    };

    this.history.push(entry);
    this.updateTopics(entry.topics);
    this.pruneHistory();
    this.saveToStorage();
  }

  extractTopics(text) {
    // Simple topic extraction (could be enhanced with NLP)
    const words = text.toLowerCase().split(/\s+/);
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
    
    return words
      .filter(word => word.length > 3 && !stopWords.has(word))
      .slice(0, 5); // Keep top 5 potential topics
  }

  updateTopics(topics) {
    topics.forEach(topic => {
      const count = this.topics.get(topic) || 0;
      this.topics.set(topic, count + 1);
    });
  }

  getRecentHistory(count = 5) {
    return this.history.slice(-count);
  }

  getFullHistory() {
    return [...this.history];
  }

  getCurrentContext() {
    const recent = this.getRecentHistory(this.contextWindow);
    const topTopics = Array.from(this.topics.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([topic]) => topic);

    return {
      recentHistory: recent,
      topTopics: topTopics,
      conversationLength: this.history.length
    };
  }

  pruneHistory() {
    if (this.history.length > this.maxHistoryLength) {
      this.history = this.history.slice(-this.maxHistoryLength);
    }
  }

  saveToStorage() {
    try {
      localStorage.setItem('agentlee_conversation_history', JSON.stringify(this.history));
    } catch (error) {
      console.warn('Could not save conversation history:', error);
    }
  }

  clear() {
    this.history = [];
    this.topics.clear();
    this.userPreferences.clear();
    localStorage.removeItem('agentlee_conversation_history');
  }

  shutdown() {
    this.saveToStorage();
  }
}
