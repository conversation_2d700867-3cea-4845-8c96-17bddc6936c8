# --- Core Server Framework ---
fastapi==0.111.0
uvicorn[standard]==0.29.0

# --- <PERSON><PERSON>er & Automation ---
playwright==1.44.0
# Required to install supported browsers after pip install
# Run separately: playwright install

# --- Speech & Audio Recognition ---
pyaudio==0.2.13         # For microphone input
SpeechRecognition==3.10.1
vosk==0.3.45             # Offline speech-to-text

# --- Voice Output / TTS ---
pyttsx3==2.90            # Offline TTS (optional)
# Optional: more advanced TTS like ElevenLabs via API

# --- System Access / Shell Commands ---
psutil==5.9.8            # System info
subprocess32; sys_platform == 'linux'

# --- Web Scraping / Search (Optional) ---
requests==2.31.0
beautifulsoup4==4.12.3
# serpapi==0.1.4          # Optional: Google search API wrapper (requires API key)

# --- Utility Libraries ---
python-dotenv==1.0.1     # For .env file loading
aiofiles==23.2.1         # For FastAPI file handling

# --- Database (Optional future upgrade) ---
# sqlite or custom local persistence handled in frontend/IndexedDB

# --- Optional Enhancements ---
# openai==1.30.1          # If fallback OpenAI needed
# google-generativeai==0.5.2  # For Gemini fallback
