{"name": "agent-lee-floating-widget", "version": "2.0.0", "description": "Agent Lee™ - True Floating Widget AI Assistant with Ultra-Low Latency Optimization", "main": "main.js", "author": "Agent Lee™ Development Team", "license": "MIT", "homepage": "https://github.com/agentlee/agent-lee-system", "keywords": ["ai", "agent", "assistant", "electron", "automation"], "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "electron-builder --publish=never", "dist-all": "electron-builder --mac --win --linux"}, "devDependencies": {"electron": "^25.8.0", "electron-builder": "^24.6.4"}, "dependencies": {"electron-log": "^4.4.8", "electron-updater": "^6.1.4"}, "build": {"appId": "com.agentlee.system", "productName": "Agent Lee™ System", "directories": {"output": "../release", "buildResources": "assets"}, "files": ["main.js", "preload.js", "../frontend/**/*", "../backend/**/*", "!../backend/venv/**/*", "!../backend/__pycache__/**/*", "!../backend/*.log"], "extraResources": [{"from": "../backend", "to": "backend", "filter": ["!venv/**/*", "!__pycache__/**/*", "!*.log"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "publisherName": "Agent Lee™ Development"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.productivity"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true}, "dmg": {"title": "Agent Lee™ System Installer", "icon": "assets/icon.icns", "iconSize": 128, "window": {"width": 540, "height": 380}, "contents": [{"x": 140, "y": 200, "type": "file"}, {"x": 400, "y": 200, "type": "link", "path": "/Applications"}]}, "publish": {"provider": "github", "owner": "agent<PERSON>", "repo": "agent-lee-system", "releaseType": "release"}}}