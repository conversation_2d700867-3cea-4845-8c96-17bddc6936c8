"""
Agent Lee™ System Controller
Real system integration and control capabilities
"""

import os
import sys
import subprocess
import platform
import webbrowser
import logging
from typing import Dict, Any, Optional
import psutil

logger = logging.getLogger("agentlee.system")

class SystemController:
    """System integration and control"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.app_registry = self._build_app_registry()
        logger.info("System Controller initialized")
    
    def _build_app_registry(self) -> Dict[str, str]:
        """Build platform-specific app registry"""
        if self.platform == "windows":
            return {
                # System apps
                "notepad": "notepad.exe",
                "calculator": "calc.exe",
                "paint": "mspaint.exe",
                "cmd": "cmd.exe",
                "powershell": "powershell.exe",
                "explorer": "explorer.exe",
                "taskmanager": "taskmgr.exe",
                
                # Browsers
                "chrome": r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                "firefox": r"C:\Program Files\Mozilla Firefox\firefox.exe",
                "edge": "msedge.exe",
                
                # Office apps
                "word": "winword.exe",
                "excel": "excel.exe",
                "powerpoint": "powerpnt.exe",
                "outlook": "outlook.exe",
                
                # Communication
                "teams": "ms-teams:",
                "discord": "discord:",
                "telegram": "tg:",
                "whatsapp": "whatsapp:",
                "skype": "skype:",
                
                # Media
                "spotify": "spotify:",
                "vlc": r"C:\Program Files\VideoLAN\VLC\vlc.exe",
                "steam": "steam:",
                
                # Development
                "vscode": "code",
                "sublime": "subl",
                "notepadpp": r"C:\Program Files\Notepad++\notepad++.exe"
            }
        elif self.platform == "darwin":  # macOS
            return {
                "safari": "open -a Safari",
                "chrome": "open -a 'Google Chrome'",
                "firefox": "open -a Firefox",
                "finder": "open -a Finder",
                "terminal": "open -a Terminal",
                "textedit": "open -a TextEdit",
                "calculator": "open -a Calculator"
            }
        else:  # Linux
            return {
                "firefox": "firefox",
                "chrome": "google-chrome",
                "nautilus": "nautilus",
                "terminal": "gnome-terminal",
                "gedit": "gedit",
                "calculator": "gnome-calculator"
            }
    
    async def open_application(self, app_name: str) -> Dict[str, Any]:
        """Open an application"""
        try:
            app_name = app_name.lower().strip()
            logger.info(f"Attempting to open application: {app_name}")
            
            if app_name in self.app_registry:
                app_path = self.app_registry[app_name]
                
                if app_path.startswith(("http:", "https:", "mailto:", "ms-", "tg:", "discord:", "whatsapp:", "spotify:", "steam:", "skype:")):
                    # Protocol handler
                    if self.platform == "windows":
                        os.startfile(app_path)
                    else:
                        subprocess.Popen(["open", app_path] if self.platform == "darwin" else ["xdg-open", app_path])
                    
                    logger.info(f"Opened {app_name} via protocol handler")
                    return {"status": "success", "app": app_name, "message": f"Opened {app_name}"}
                else:
                    # Regular executable
                    if self.platform == "darwin" and app_path.startswith("open"):
                        subprocess.Popen(app_path.split())
                    else:
                        subprocess.Popen(app_path, shell=True)

                    logger.info(f"Opened {app_name} application")
                    return {"status": "success", "app": app_name, "message": f"Opened {app_name}"}
            else:
                # Try to find app in system PATH
                try:
                    subprocess.Popen([app_name])
                    logger.info(f"Opened {app_name} from PATH")
                    return {"status": "success", "app": app_name, "message": f"Opened {app_name}"}
                except FileNotFoundError:
                    logger.warning(f"Application '{app_name}' not found")
                    return {"status": "error", "message": f"Application '{app_name}' not found"}

        except Exception as e:
            logger.error(f"Error opening {app_name}: {e}")
            return {"status": "error", "message": f"Failed to open {app_name}: {str(e)}"}
    
    async def search_web(self, query: str, search_engine: str = "google") -> Dict[str, Any]:
        """Perform web search"""
        try:
            search_urls = {
                "google": f"https://www.google.com/search?q={query}",
                "bing": f"https://www.bing.com/search?q={query}",
                "duckduckgo": f"https://duckduckgo.com/?q={query}",
                "yahoo": f"https://search.yahoo.com/search?p={query}"
            }
            
            url = search_urls.get(search_engine.lower(), search_urls["google"])
            webbrowser.open(url)
            
            logger.info(f"Opened web search for: {query}")
            return {
                "status": "success",
                "query": query,
                "search_engine": search_engine,
                "message": f"Opened {search_engine} search for '{query}'"
            }

        except Exception as e:
            logger.error(f"Web search error: {e}")
            return {"status": "error", "message": f"Failed to search: {str(e)}"}
    
    async def get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        try:
            # CPU info
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Memory info
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_total = round(memory.total / (1024**3), 2)  # GB
            memory_used = round(memory.used / (1024**3), 2)   # GB
            
            # Disk info
            disk = psutil.disk_usage('/')
            disk_percent = round((disk.used / disk.total) * 100, 2)
            disk_total = round(disk.total / (1024**3), 2)  # GB
            disk_free = round(disk.free / (1024**3), 2)   # GB
            
            return {
                "status": "success",
                "system": {
                    "platform": platform.system(),
                    "platform_version": platform.version(),
                    "architecture": platform.architecture()[0],
                    "processor": platform.processor(),
                    "hostname": platform.node()
                },
                "performance": {
                    "cpu_percent": cpu_percent,
                    "cpu_count": cpu_count,
                    "memory_percent": memory_percent,
                    "memory_total_gb": memory_total,
                    "memory_used_gb": memory_used,
                    "disk_percent": disk_percent,
                    "disk_total_gb": disk_total,
                    "disk_free_gb": disk_free
                }
            }
            
        except Exception as e:
            logger.error(f"System info error: {e}")
            return {"status": "error", "message": f"Failed to get system info: {str(e)}"}
    
    async def execute_command(self, command: str, safe_mode: bool = True) -> Dict[str, Any]:
        """Execute system command (with safety checks)"""
        try:
            if safe_mode:
                # Whitelist of safe commands
                safe_commands = [
                    "dir", "ls", "pwd", "whoami", "date", "time", 
                    "echo", "ping", "ipconfig", "ifconfig", "ps"
                ]
                
                command_base = command.split()[0].lower()
                if command_base not in safe_commands:
                    return {
                        "status": "error", 
                        "message": f"Command '{command_base}' not allowed in safe mode"
                    }
            
            # Execute command
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=30
            )
            
            return {
                "status": "success",
                "command": command,
                "output": result.stdout,
                "error": result.stderr,
                "return_code": result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {"status": "error", "message": "Command timed out"}
        except Exception as e:
            logger.error(f"Command execution error: {e}")
            return {"status": "error", "message": f"Failed to execute command: {str(e)}"}

    def open_messaging_app(self, app_name="telegram"):
        """Open messaging applications like Telegram, WhatsApp, etc."""
        try:
            messaging_apps = {
                "telegram": {
                    "windows": "telegram.exe",
                    "darwin": "Telegram",
                    "linux": "telegram-desktop"
                },
                "whatsapp": {
                    "windows": "WhatsApp.exe",
                    "darwin": "WhatsApp",
                    "linux": "whatsapp-for-linux"
                },
                "discord": {
                    "windows": "Discord.exe",
                    "darwin": "Discord",
                    "linux": "discord"
                },
                "slack": {
                    "windows": "slack.exe",
                    "darwin": "Slack",
                    "linux": "slack"
                }
            }

            app_name = app_name.lower()
            if app_name in messaging_apps:
                app_info = messaging_apps[app_name]
                executable = app_info.get(self.platform, app_name)

                # Try to open the application
                if self.platform == "windows":
                    subprocess.Popen(executable, shell=True)
                elif self.platform == "darwin":
                    subprocess.Popen(["open", "-a", executable])
                else:  # Linux
                    subprocess.Popen([executable])

                logger.info(f"Opened {app_name} messaging app")
                return {
                    "status": "success",
                    "app": app_name,
                    "message": f"Opened {app_name.title()}"
                }
            else:
                return {
                    "status": "error",
                    "message": f"Messaging app '{app_name}' not supported. Available: {list(messaging_apps.keys())}"
                }

        except Exception as e:
            logger.error(f"Error opening messaging app {app_name}: {e}")
            return {"status": "error", "message": f"Failed to open {app_name}: {str(e)}"}

    def open_email(self, to="", subject="", body=""):
        """Open email client with optional pre-filled fields"""
        try:
            if to or subject or body:
                # Create mailto URL
                mailto_url = f"mailto:{to}?subject={subject}&body={body}"
                if self.platform == "windows":
                    subprocess.Popen(["start", mailto_url], shell=True)
                elif self.platform == "darwin":
                    subprocess.Popen(["open", mailto_url])
                else:  # Linux
                    subprocess.Popen(["xdg-open", mailto_url])
            else:
                # Open default email client
                if self.platform == "windows":
                    subprocess.Popen(["start", "mailto:"], shell=True)
                elif self.platform == "darwin":
                    subprocess.Popen(["open", "-a", "Mail"])
                else:  # Linux
                    subprocess.Popen(["xdg-open", "mailto:"])

            logger.info("Opened email client")
            return {
                "status": "success",
                "message": "Email client opened"
            }

        except Exception as e:
            logger.error(f"Error opening email: {e}")
            return {"status": "error", "message": f"Failed to open email: {str(e)}"}

    def list_available_apps(self):
        """Get list of available applications"""
        try:
            available_apps = []

            # Add common applications based on platform
            if self.platform == "windows":
                common_apps = [
                    "notepad", "calculator", "mspaint", "explorer",
                    "chrome", "firefox", "telegram", "whatsapp", "discord"
                ]
            elif self.platform == "darwin":
                common_apps = [
                    "TextEdit", "Calculator", "Finder", "Safari",
                    "Chrome", "Firefox", "Telegram", "WhatsApp", "Discord"
                ]
            else:  # Linux
                common_apps = [
                    "gedit", "calculator", "nautilus", "firefox",
                    "chromium", "telegram-desktop", "discord"
                ]

            # Add apps from our registry
            available_apps.extend(list(self.app_registry.keys()))
            available_apps.extend(common_apps)

            # Remove duplicates and sort
            available_apps = sorted(list(set(available_apps)))

            return {
                "status": "success",
                "apps": available_apps,
                "platform": self.platform
            }

        except Exception as e:
            logger.error(f"Error listing apps: {e}")
            return {"status": "error", "message": f"Failed to list apps: {str(e)}"}
    
    def get_available_apps(self) -> list:
        """Get list of available applications"""
        return list(self.app_registry.keys())
    
    def get_controller_status(self) -> Dict[str, Any]:
        """Get controller status"""
        return {
            "platform": self.platform,
            "available_apps": len(self.app_registry),
            "app_list": list(self.app_registry.keys())
        }

# Global system controller instance
system_controller = SystemController()
