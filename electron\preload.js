const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

/**
 * Secure context bridge for safely exposing Electron APIs to renderer
 */
contextBridge.exposeInMainWorld('electronAPI', {
  // Backend related functions
  checkBackendStatus: () => ipcRenderer.invoke('check-backend'),
  restartBackend: () => ipcRenderer.invoke('restart-backend'),
  
  // System-related functions for the Agent Lee interface
  getAppVersion: () => process.env.npm_package_version || '1.0.0',
  getPlatform: () => process.platform,

  // Enhanced Window controls for floating widget
  moveWindow: (deltaX, deltaY) => ipcRenderer.invoke('move-window', deltaX, deltaY),
  closeApp: () => ipcRenderer.invoke('close-app'),
  toggleAlwaysOnTop: () => ipcRenderer.invoke('toggle-always-on-top'),
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  resizeWindow: (width, height) => ipcRenderer.invoke('resize-window', width, height),
  setWindowPosition: (x, y) => ipcRenderer.invoke('set-window-position', x, y),
  getWindowBounds: () => ipcRenderer.invoke('get-window-bounds'),
  setWindowOpacity: (opacity) => ipcRenderer.invoke('set-window-opacity', opacity),
  
  // API interface for communicating with backend
  apiRequest: async (endpoint, method = 'GET', data = null) => {
    try {
      const baseUrl = 'http://localhost:8000';
      const url = `${baseUrl}${endpoint}`;
      
      const options = {
        method,
        headers: {
          'Content-Type': 'application/json'
        }
      };
      
      if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data);
      }
      
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }
});

// Add floating widget specific functionality
contextBridge.exposeInMainWorld('floatingWidget', {
  // Make window draggable
  enableDragging: (elementId) => {
    const element = document.getElementById(elementId);
    if (!element) return;

    let isDragging = false;
    let startX, startY;

    element.addEventListener('mousedown', (e) => {
      isDragging = true;
      startX = e.screenX;
      startY = e.screenY;

      const handleMouseMove = (e) => {
        if (!isDragging) return;

        const deltaX = e.screenX - startX;
        const deltaY = e.screenY - startY;

        ipcRenderer.invoke('get-window-bounds').then(bounds => {
          ipcRenderer.invoke('set-window-position',
            bounds.x + deltaX,
            bounds.y + deltaY
          );
        });

        startX = e.screenX;
        startY = e.screenY;
      };

      const handleMouseUp = () => {
        isDragging = false;
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    });
  },

  // Window transparency effects
  setTransparency: (opacity) => {
    ipcRenderer.invoke('set-window-opacity', opacity);
  },

  // Always on top toggle
  toggleStayOnTop: () => {
    return ipcRenderer.invoke('toggle-always-on-top');
  }
});

console.log('[Preload] Enhanced floating widget API loaded');