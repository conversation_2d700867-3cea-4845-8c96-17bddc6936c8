"""
Agent Lee™ Mod Loader
Integrates Agent <PERSON> mods in clusters of 4 for enhanced functionality
"""

import os
import sys
import importlib.util
import logging
from pathlib import Path
from typing import Dict, List, Any

logger = logging.getLogger("agentlee.mods")

class ModCluster:
    """Represents a cluster of 4 related mods"""
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.modules = []
        self.loaded = False
        self.status = "inactive"
    
    def add_module(self, module_path: str, module_type: str = "python"):
        """Add a module to this cluster"""
        self.modules.append({
            "path": module_path,
            "type": module_type,
            "loaded": False,
            "instance": None
        })
    
    def load_cluster(self):
        """Load all modules in this cluster"""
        try:
            logger.info(f"Loading mod cluster: {self.name}")
            for module in self.modules:
                if module["type"] == "python":
                    self._load_python_module(module)
                elif module["type"] == "javascript":
                    self._register_js_module(module)
            
            self.loaded = True
            self.status = "active"
            logger.info(f"✅ Cluster '{self.name}' loaded successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to load cluster '{self.name}': {e}")
            self.status = "error"
            return False
    
    def _load_python_module(self, module):
        """Load a Python module"""
        try:
            module_path = Path(module["path"])
            if module_path.exists():
                spec = importlib.util.spec_from_file_location(
                    module_path.stem, module_path
                )
                if spec and spec.loader:
                    mod = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(mod)
                    module["instance"] = mod
                    module["loaded"] = True
                    logger.debug(f"Loaded Python module: {module_path.name}")
        except Exception as e:
            logger.warning(f"Failed to load Python module {module['path']}: {e}")
    
    def _register_js_module(self, module):
        """Register a JavaScript module for frontend integration"""
        try:
            module["loaded"] = True
            logger.debug(f"Registered JS module: {Path(module['path']).name}")
        except Exception as e:
            logger.warning(f"Failed to register JS module {module['path']}: {e}")

class ModLoader:
    """Main mod loader that organizes mods into clusters of 4"""
    
    def __init__(self):
        self.clusters = {}
        self.mods_path = Path(__file__).parent.parent / "agentleemods1thru4"
        self.setup_clusters()
    
    def setup_clusters(self):
        """Setup mod clusters organized by functionality"""
        
        # Cluster 1: Core System & Permissions (Modules 9-12)
        cluster1 = ModCluster(
            "core_system", 
            "Core system permissions and initialization"
        )
        cluster1.add_module(str(self.mods_path / "module_09_system_permissions.py"))
        cluster1.add_module(str(self.mods_path / "module_10_indexdb_sync.py"))
        cluster1.add_module(str(self.mods_path / "module_11_privacy_guard.py"))
        cluster1.add_module(str(self.mods_path / "module_12_startup_initializer.py"))
        self.clusters["core_system"] = cluster1
        
        # Cluster 2: App Control & File Management (Modules 13-16)
        cluster2 = ModCluster(
            "app_control", 
            "Application control and file management"
        )
        cluster2.add_module(str(self.mods_path / "module_13_app_controller.py"))
        cluster2.add_module(str(self.mods_path / "module_14_file_locator.py"))
        cluster2.add_module(str(self.mods_path / "module_15_voice_actions.py"))
        # Note: module_16 is a database file, handled separately
        self.clusters["app_control"] = cluster2
        
        # Cluster 3: Analytics & Advanced Features (Modules 17-20)
        cluster3 = ModCluster(
            "analytics", 
            "System analytics and advanced features"
        )
        cluster3.add_module(str(self.mods_path / "17_agentlee_analytics.py"))
        cluster3.add_module(str(self.mods_path / "18_agentlee_voice_synthesis.py"))
        cluster3.add_module(str(self.mods_path / "19_agentlee_autorun.py"))
        cluster3.add_module(str(self.mods_path / "20_agentlee_localsearch.py"))
        self.clusters["analytics"] = cluster3
        
        # Cluster 4: Memory & Monitoring (Modules 21-24)
        cluster4 = ModCluster(
            "memory_monitoring", 
            "Memory management and system monitoring"
        )
        cluster4.add_module(str(self.mods_path / "21_agentlee_memory.py"))
        cluster4.add_module(str(self.mods_path / "22_agentlee_scheduler.py"))
        cluster4.add_module(str(self.mods_path / "23_agentlee_filewatch.py"))
        cluster4.add_module(str(self.mods_path / "24_agentlee_cloudsync.py"))
        self.clusters["memory_monitoring"] = cluster4
        
        # Cluster 5: Task Management (Modules 25-28)
        cluster5 = ModCluster(
            "task_management", 
            "Task handling and permission enforcement"
        )
        cluster5.add_module(str(self.mods_path / "module25_task_handler.py"))
        cluster5.add_module(str(self.mods_path / "module26_event_listener.py"))
        cluster5.add_module(str(self.mods_path / "module27_permission_enforcer.py"))
        cluster5.add_module(str(self.mods_path / "module28_profile_loader.py"))
        self.clusters["task_management"] = cluster5
        
        # Cluster 6: AI Frontend Control (Modules 29-32)
        cluster6 = ModCluster(
            "ai_frontend", 
            "AI-powered frontend control and routing"
        )
        cluster6.add_module(str(self.mods_path / "Module29_AIActionRouter.js"), "javascript")
        cluster6.add_module(str(self.mods_path / "Module30_MemoryLedger.js"), "javascript")
        cluster6.add_module(str(self.mods_path / "Module31_UIControlMatrix.js"), "javascript")
        cluster6.add_module(str(self.mods_path / "Module32_TimelineInferenceEngine.js"), "javascript")
        self.clusters["ai_frontend"] = cluster6
        
        # Cluster 7: System Integration (Modules 33-36)
        cluster7 = ModCluster(
            "system_integration", 
            "Device control and system integration"
        )
        cluster7.add_module(str(self.mods_path / "Module33_MemorySyncDaemon.js"), "javascript")
        cluster7.add_module(str(self.mods_path / "Module34_DeviceControlInterface.js"), "javascript")
        cluster7.add_module(str(self.mods_path / "Module35_SecurityPolicyEngine.js"), "javascript")
        cluster7.add_module(str(self.mods_path / "Module36_ObservationTracker.js"), "javascript")
        self.clusters["system_integration"] = cluster7
        
        # Cluster 8: AI Voice & Advanced Features (Modules 45-48)
        cluster8 = ModCluster(
            "ai_voice", 
            "AI voice features and advanced capabilities"
        )
        cluster8.add_module(str(self.mods_path / "AgentLee_Module_45.py"))
        cluster8.add_module(str(self.mods_path / "AgentLee_Module_46.py"))
        cluster8.add_module(str(self.mods_path / "AgentLee_Module_47.py"))
        cluster8.add_module(str(self.mods_path / "AgentLee_Module_48.py"))
        self.clusters["ai_voice"] = cluster8
    
    def load_cluster(self, cluster_name: str) -> bool:
        """Load a specific cluster"""
        if cluster_name in self.clusters:
            return self.clusters[cluster_name].load_cluster()
        else:
            logger.error(f"Cluster '{cluster_name}' not found")
            return False
    
    def load_all_clusters(self) -> Dict[str, bool]:
        """Load all mod clusters"""
        results = {}
        logger.info("🚀 Loading Agent Lee mod clusters...")
        
        for cluster_name, cluster in self.clusters.items():
            results[cluster_name] = cluster.load_cluster()
        
        loaded_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        logger.info(f"✅ Loaded {loaded_count}/{total_count} mod clusters successfully")
        return results
    
    def get_cluster_status(self) -> Dict[str, Any]:
        """Get status of all clusters"""
        status = {}
        for name, cluster in self.clusters.items():
            status[name] = {
                "name": cluster.name,
                "description": cluster.description,
                "loaded": cluster.loaded,
                "status": cluster.status,
                "module_count": len(cluster.modules),
                "loaded_modules": sum(1 for m in cluster.modules if m["loaded"])
            }
        return status

# Global mod loader instance
mod_loader = ModLoader()
