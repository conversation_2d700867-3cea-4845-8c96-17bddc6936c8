/**
 * 🖥️ Electron Main Process - Cross-Platform Floating Widget
 * Creates transparent, frameless window for true floating widget experience
 */

const { app, BrowserWindow, screen, ipcMain } = require('electron');
const path = require('path');

let mainWindow;
let isAlwaysOnTop = true;

function createWindow() {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 390,
    height: 590,
    x: width - 440, // Position near right edge
    y: 50,
    frame: false, // Remove window frame
    transparent: true, // Transparent background
    alwaysOnTop: isAlwaysOnTop,
    resizable: true,
    minimizable: true,
    maximizable: false,
    skipTaskbar: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    show: false // Don't show until ready
  });

  // Load the app
  mainWindow.loadFile(path.join(__dirname, '../index.html'));

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Optional: Start with window focused
    if (process.platform === 'darwin') {
      app.dock.show();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Make window draggable from any point
  mainWindow.setMovable(true);

  // Handle always on top toggle
  ipcMain.handle('toggle-always-on-top', () => {
    isAlwaysOnTop = !isAlwaysOnTop;
    mainWindow.setAlwaysOnTop(isAlwaysOnTop);
    return isAlwaysOnTop;
  });

  // Handle window minimize
  ipcMain.handle('minimize-window', () => {
    mainWindow.minimize();
  });

  // Handle window close
  ipcMain.handle('close-window', () => {
    mainWindow.close();
  });

  // Handle window resize
  ipcMain.handle('resize-window', (event, width, height) => {
    mainWindow.setSize(width, height);
  });

  // Handle window position
  ipcMain.handle('set-window-position', (event, x, y) => {
    mainWindow.setPosition(x, y);
  });

  // Get window bounds
  ipcMain.handle('get-window-bounds', () => {
    return mainWindow.getBounds();
  });

  // Set window opacity
  ipcMain.handle('set-window-opacity', (event, opacity) => {
    mainWindow.setOpacity(opacity);
  });

  // Development tools
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools({ mode: 'detach' });
  }
}

// App event handlers
app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
  });
});

// Handle certificate errors
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (url.startsWith('https://localhost') || url.startsWith('https://127.0.0.1')) {
    // Allow localhost certificates for development
    event.preventDefault();
    callback(true);
  } else {
    callback(false);
  }
});

// App info
app.setName('Agent Lee');
app.setVersion('2.0.0');

// macOS specific
if (process.platform === 'darwin') {
  app.dock.setIcon(path.join(__dirname, '../assets/icon.png'));
}
