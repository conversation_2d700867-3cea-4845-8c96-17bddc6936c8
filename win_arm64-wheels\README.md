# Experimental Wheels for Python for Windows on ARM64

This repository provides experimental binary wheels for open-source extension packages
for [Python for Windows on ARM64](https://www.python.org/downloads/windows/).

The files are experimental (meaning: unofficial, informal, unrecognized, unsupported,
no warranty, no liability, provided "as is") and made available for testing and
evaluation purposes. They are not intended for production use.

Most binaries are built from source code found in the Python Package Index or
the projects' public code repositories.

Refer to the documentation of the individual [packages](#wheels) and
[libraries](#libraries) for license restrictions and dependencies.

The wheels can be downloaded from the
[Releases](https://github.com/cgohlke/win_arm64-wheels/releases) page.

## Release 2025.3.31

### Notes

- 256 packages for Python 3.13 for Windows on ARM64
- Scipy stack: numpy with OpenBLAS, scipy, matplotlib, Pandas, scikit-learn, scikit-image, etc.
- GIS stack: GDAL, netCDF4, pyproj, Shapely, rasterio, basemap, Fiona, etc.
- Image IO: Imagecodecs, Pillow, OpenImageIO, OpenEXR, pylibCZIrw, etc.
- Noteworthy: <PERSON><PERSON>, opencv_python_headless, pymol-open-source, PyICU
- Sorry, no llvmlite and numba

### Wheels

Binary wheels for the following packages for Python 3.13 are included in the
[release](https://github.com/cgohlke/win_arm64-wheels/releases/tag/v2025.3.31):

- [aggdraw](https://pypi.org/project/aggdraw/) 1.3.19
- [aicspylibczi](https://pypi.org/project/aicspylibczi/) 3.2.1
- [aiohttp](https://pypi.org/project/aiohttp/) 3.11.14
- [akima](https://pypi.org/project/akima/) 2025.1.1
- [apsw](https://pypi.org/project/apsw/) ********
- [argon2_cffi_bindings](https://pypi.org/project/argon2_cffi_bindings/) 21.2.0
- [astropy](https://pypi.org/project/astropy/) 7.0.1
- [asv](https://pypi.org/project/asv/) 0.6.4
- [atom](https://pypi.org/project/atom/) 0.11.0
- [basemap](https://pypi.org/project/basemap/) 1.4.1
- [bcrypt](https://pypi.org/project/bcrypt/) 4.3.0
- [biopython](https://pypi.org/project/biopython/) 1.85
- [bitarray](https://pypi.org/project/bitarray/) 3.3.0
- [bitshuffle](https://pypi.org/project/bitshuffle/) 0.5.2
- [blis](https://pypi.org/project/blis/) 1.2.0
- [blosc2](https://pypi.org/project/blosc2/) 3.2.1
- [blosc](https://pypi.org/project/blosc/) 1.11.2
- [boost_histogram](https://pypi.org/project/boost_histogram/) 1.5.2
- [Bottleneck](https://pypi.org/project/Bottleneck/) 1.4.2
- [Brotli](https://pypi.org/project/Brotli/) 1.1.0
- [bsddb3](https://pypi.org/project/bsddb3/) 6.2.9
- [bsdiff4](https://pypi.org/project/bsdiff4/) 1.2.6
- [BTrees](https://pypi.org/project/BTrees/) 6.1
- [cairocffi](https://pypi.org/project/cairocffi/) 1.7.1
- [Cartopy](https://pypi.org/project/Cartopy/) 0.24.1
- [casa_formats_io](https://pypi.org/project/casa_formats_io/) 0.3.0
- [celiagg](https://pypi.org/project/celiagg/) 2.1.6
- [centrosome](https://pypi.org/project/centrosome/) 1.3.1
- [cf_units](https://pypi.org/project/cf_units/) 3.3.0
- [cffi](https://pypi.org/project/cffi/) 1.17.1
- [cftime](https://pypi.org/project/cftime/) 1.6.4.post1
- [chebyfit](https://pypi.org/project/chebyfit/) 2025.1.1
- [clarabel](https://pypi.org/project/clarabel/) 0.10.0
- [cmarkgfm](https://pypi.org/project/cmarkgfm/) 2024.11.20
- [contourpy](https://pypi.org/project/contourpy/) 1.3.1
- [coverage](https://pypi.org/project/coverage/) 7.8.0
- [cramjam](https://pypi.org/project/cramjam/) 2.8.3
- [cvxpy](https://pypi.org/project/cvxpy/) 1.6.4
- [cx_Logging](https://pypi.org/project/cx_Logging/) 3.2.1
- [cymem](https://pypi.org/project/cymem/) 2.0.11
- [cython](https://pypi.org/project/cython/) 3.0.12
- [cytoolz](https://pypi.org/project/cytoolz/) 1.0.1
- [debugpy](https://pypi.org/project/debugpy/) 1.8.13
- [deflate](https://pypi.org/project/deflate/) 0.8.0
- [dipy](https://pypi.org/project/dipy/) 1.11.0
- [discretize](https://pypi.org/project/discretize/) 0.11.2
- [dtaidistance](https://pypi.org/project/dtaidistance/) 2.3.13
- [dulwich](https://pypi.org/project/dulwich/) 0.22.8
- [enaml](https://pypi.org/project/enaml/) 0.18.0
- [ephem](https://pypi.org/project/ephem/) 4.2
- [ewah_bool_utils](https://pypi.org/project/ewah_bool_utils/) 1.2.2
- [fabio](https://pypi.org/project/fabio/) 2024.9.0
- [fast_histogram](https://pypi.org/project/fast_histogram/) 0.14
- [fastcluster](https://pypi.org/project/fastcluster/) 1.2.6
- [fastparquet](https://pypi.org/project/fastparquet/) 2024.11.0
- [fastremap](https://pypi.org/project/fastremap/) 1.15.1
- [fastrlock](https://pypi.org/project/fastrlock/) 0.8.3
- [fasttext](https://pypi.org/project/fasttext/) 0.9.3
- [fiona](https://pypi.org/project/fiona/) 1.10.1
- [fisx](https://pypi.org/project/fisx/) 1.3.2
- [fonttools](https://pypi.org/project/fonttools/) 4.56.0
- [fpzip](https://pypi.org/project/fpzip/) 1.2.5
- [freetype_py](https://pypi.org/project/freetype_py/) 2.5.1
- [frozendict](https://pypi.org/project/frozendict/) 2.4.6
- [frozenlist](https://pypi.org/project/frozenlist/) 1.5.0
- [gdal](https://pypi.org/project/gdal/) 3.10.2
- [gevent](https://pypi.org/project/gevent/) 24.11.1
- [glcontext](https://pypi.org/project/glcontext/) 3.0.0
- [glfw](https://pypi.org/project/glfw/) 2.8.0
- [glumpy](https://pypi.org/project/glumpy/) 1.2.1
- [glymur](https://pypi.org/project/glymur/) 0.14.0.post1
- [google_crc32c](https://pypi.org/project/google_crc32c/) 1.7.1
- [greenlet](https://pypi.org/project/greenlet/) 3.1.1
- [h5py](https://pypi.org/project/h5py/) 3.13.0
- [hdbscan](https://pypi.org/project/hdbscan/) 0.8.40
- [hmmlearn](https://pypi.org/project/hmmlearn/) 0.3.3
- [imagecodecs](https://pypi.org/project/imagecodecs/) 2025.3.30
- [imread](https://pypi.org/project/imread/) 0.7.6
- [indexed_gzip](https://pypi.org/project/indexed_gzip/) 1.9.4
- [intbitset](https://pypi.org/project/intbitset/) 4.0.0
- [JCC](https://pypi.org/project/JCC/) 3.15
- [jellyfish](https://pypi.org/project/jellyfish/) 1.2.0
- [jpype1](https://pypi.org/project/jpype1/) 1.5.2
- [jsonobject](https://pypi.org/project/jsonobject/) 2.3.1
- [kivy](https://pypi.org/project/kivy/) 2.3.1
- [kiwisolver](https://pypi.org/project/kiwisolver/) 1.4.8
- [lazy_object_proxy](https://pypi.org/project/lazy_object_proxy/) 1.10.0
- [lfdfiles](https://pypi.org/project/lfdfiles/) 2025.3.16
- [libcst](https://pypi.org/project/libcst/) 1.7.0
- [lightgbm](https://pypi.org/project/lightgbm/) 4.6.0
- [line_profiler](https://pypi.org/project/line_profiler/) 4.2.0
- [lmdb](https://pypi.org/project/lmdb/) 1.6.2
- [logbook](https://pypi.org/project/logbook/) 1.8.1
- [lxml](https://pypi.org/project/lxml/) 5.3.1
- [lz4](https://pypi.org/project/lz4/) 4.4.3
- [mahotas](https://pypi.org/project/mahotas/) 1.4.18
- [marisa_trie](https://pypi.org/project/marisa_trie/) 1.2.1
- [MarkupSafe](https://pypi.org/project/MarkupSafe/) 3.0.2
- [matplotlib](https://pypi.org/project/matplotlib/) 3.10.1
- [maturin](https://pypi.org/project/maturin/) 1.8.3
- [mercurial](https://pypi.org/project/mercurial/) 6.9.4
- [meshpy](https://pypi.org/project/meshpy/) 2025.1.1
- [ml_dtypes](https://pypi.org/project/ml_dtypes/) 0.5.1
- [moderngl](https://pypi.org/project/moderngl/) 5.12.0
- [mplcairo](https://pypi.org/project/mplcairo/) 0.6.1
- [msgpack](https://pypi.org/project/msgpack/) 1.1.0
- [msvc_runtime](https://pypi.org/project/msvc_runtime/) 14.42.34433
- [multidict](https://pypi.org/project/multidict/) 6.2.0
- [murmurhash](https://pypi.org/project/murmurhash/) 1.0.12
- [ndindex](https://pypi.org/project/ndindex/) 1.9.2
- [netcdf4](https://pypi.org/project/netcdf4/) 1.7.2
- [nh3](https://pypi.org/project/nh3/) 0.2.21
- [noise](https://pypi.org/project/noise/) 1.2.3
- [numcodecs](https://pypi.org/project/numcodecs/) 0.15.1
- [numexpr](https://pypi.org/project/numexpr/) 2.10.2
- [numpy](https://pypi.org/project/numpy/) 2.2.4
- [numpy_quaternion](https://pypi.org/project/numpy_quaternion/) 2024.0.7
- [opencv_python_headless](https://pypi.org/project/opencv_python_headless/) 4.10.0.84
- [openexr](https://pypi.org/project/openexr/) 3.3.3
- [openimageio](https://pypi.org/project/openimageio/) *******
- [openslide_python](https://pypi.org/project/openslide_python/) 1.4.1
- [openTSNE](https://pypi.org/project/openTSNE/) 1.0.2
- [optree](https://pypi.org/project/optree/) 0.14.1
- [orjson](https://pypi.org/project/orjson/) 3.10.16
- [osqp](https://pypi.org/project/osqp/) 1.0.1
- [pandas](https://pypi.org/project/pandas/) 2.2.3
- [pcodec](https://pypi.org/project/pcodec/) 0.3.3
- [peewee](https://pypi.org/project/peewee/) 3.17.9
- [persistent](https://pypi.org/project/persistent/) 6.1.1
- [phasorpy](https://pypi.org/project/phasorpy/) 0.4
- [pillow](https://pypi.org/project/pillow/) 11.1.0
- [pillow_avif_plugin](https://pypi.org/project/pillow_avif_plugin/) 1.5.1
- [pocketsphinx](https://pypi.org/project/pocketsphinx/) 5.0.4
- [polars](https://pypi.org/project/polars/) 1.26.0
- [protobuf](https://pypi.org/project/protobuf/) 5.28.3
- [psd_tools](https://pypi.org/project/psd_tools/) 1.10.7
- [psf](https://pypi.org/project/psf/) 2025.1.1
- [psutil](https://pypi.org/project/psutil/) 7.0.0
- [psycopg2](https://pypi.org/project/psycopg2/) 2.9.10
- [ptufile](https://pypi.org/project/ptufile/) 2025.2.20
- [pyamg](https://pypi.org/project/pyamg/) 5.2.1
- [pyaudio](https://pypi.org/project/pyaudio/) 0.2.14
- [pycairo](https://pypi.org/project/pycairo/) 1.27.0
- [pycares](https://pypi.org/project/pycares/) 4.5.0
- [pycifrw](https://pypi.org/project/pycifrw/) 5.0.1
- [pycrdt](https://pypi.org/project/pycrdt/) 0.12.10
- [pycurl](https://pypi.org/project/pycurl/) 7.45.6
- [pydantic_core](https://pypi.org/project/pydantic_core/) 2.33.0
- [pyerfa](https://pypi.org/project/pyerfa/) 2.0.1.5
- [pyfltk](https://pypi.org/project/pyfltk/) 1.4.2.0
- [pygame](https://pypi.org/project/pygame/) 2.6.1
- [pygeos](https://pypi.org/project/pygeos/) 0.14.0
- [pygit2](https://pypi.org/project/pygit2/) 1.17.0
- [pygresql](https://pypi.org/project/pygresql/) 6.1.0
- [pyhdf](https://pypi.org/project/pyhdf/) 0.11.6
- [pyicu](https://pypi.org/project/pyicu/) 2.15
- [pyjnius](https://pypi.org/project/pyjnius/) 1.6.1
- [pylibCZIrw](https://pypi.org/project/pylibCZIrw/) 4.1.3
- [pylibjpeg_openjpeg](https://pypi.org/project/pylibjpeg_openjpeg/) 2.4.0
- [pylibjpeg_rle](https://pypi.org/project/pylibjpeg_rle/) 2.0.0
- [pyliblzfse](https://pypi.org/project/pyliblzfse/) 0.4.1
- [pylibtiff](https://pypi.org/project/pylibtiff/) 0.6.1
- [pymatgen](https://pypi.org/project/pymatgen/) 2025.3.10
- [PyMCubes](https://pypi.org/project/PyMCubes/) 0.1.6
- [pymmcore](https://pypi.org/project/pymmcore/) 11.5.0.73.0
- [pymol](https://github.com/schrodinger/pymol-open-source) 3.1.0
- [pymol_launcher](https://pypi.org/project/pymol_launcher/) 3.1
- [pymongo](https://pypi.org/project/pymongo/) 4.11.3
- [pymssql](https://pypi.org/project/pymssql/) 2.3.2
- [pyodbc](https://pypi.org/project/pyodbc/) 5.2.0
- [pyogrio](https://pypi.org/project/pyogrio/) 0.10.0
- [pyopencl](https://pypi.org/project/pyopencl/) 2025.1
- [pyopengl](https://pypi.org/project/pyopengl/) 3.1.9
- [pyopengl_accelerate](https://pypi.org/project/pyopengl_accelerate/) 3.1.9
- [pypmc](https://pypi.org/project/pypmc/) 1.2.4
- [pyproj](https://pypi.org/project/pyproj/) 3.7.1
- [pyreadstat](https://pypi.org/project/pyreadstat/) 1.2.8
- [pystackreg](https://pypi.org/project/pystackreg/) 0.2.8
- [pystemmer](https://pypi.org/project/pystemmer/) 2.2.0.3
- [pytensor](https://pypi.org/project/pytensor/) 2.30.1
- [python_box](https://pypi.org/project/python_box/) 7.3.2
- [python_curses](https://pypi.org/project/python_curses/) 2.2.3
- [python_javabridge](https://pypi.org/project/python_javabridge/) 4.0.4
- [python_ldap](https://pypi.org/project/python_ldap/) 3.4.4
- [python_lzf](https://pypi.org/project/python_lzf/) 0.2.6
- [python_rapidjson](https://pypi.org/project/python_rapidjson/) 1.20
- [python_snappy](https://pypi.org/project/python_snappy/) 0.6.1
- [PyTurboJPEG](https://pypi.org/project/PyTurboJPEG/) 1.7.7
- [pywavelets](https://pypi.org/project/pywavelets/) 1.8.0
- [pywin32](https://pypi.org/project/pywin32/) 310
- [pywinpty](https://pypi.org/project/pywinpty/) 2.0.15
- [PyYAML](https://pypi.org/project/PyYAML/) 6.0.2
- [pyzmq](https://pypi.org/project/pyzmq/) 26.3.0
- [pyzstd](https://pypi.org/project/pyzstd/) 0.16.2
- [qutip](https://pypi.org/project/qutip/) 5.1.1
- [rapidfuzz](https://pypi.org/project/rapidfuzz/) 3.12.2
- [rasterio](https://pypi.org/project/rasterio/) 1.4.3
- [recordclass](https://pypi.org/project/recordclass/) 0.22.1
- [regex](https://pypi.org/project/regex/) 2024.11.6
- [rosettasciio](https://pypi.org/project/rosettasciio/) 0.8.0
- [rpds_py](https://pypi.org/project/rpds_py/) 0.24.0
- [rtree](https://pypi.org/project/rtree/) 1.4.0
- [ruamel_yaml_clib](https://pypi.org/project/ruamel_yaml_clib/) 0.2.12
- [scalene](https://pypi.org/project/scalene/) 1.5.51
- [scikit_image](https://pypi.org/project/scikit_image/) 0.25.2
- [scikit_learn](https://pypi.org/project/scikit_learn/) 1.6.1
- [scipy](https://pypi.org/project/scipy/) 1.15.2
- [scs](https://pypi.org/project/scs/) 3.2.7
- [setproctitle](https://pypi.org/project/setproctitle/) 1.3.5
- [sfepy](https://pypi.org/project/sfepy/) 2025.1
- [shapely](https://pypi.org/project/shapely/) 2.0.7
- [simplejson](https://pypi.org/project/simplejson/) 3.20.1
- [siphash24](https://pypi.org/project/siphash24/) 1.7
- [sounddevice](https://pypi.org/project/sounddevice/) 0.5.1
- [soundfile](https://pypi.org/project/soundfile/) 0.13.1
- [spectrum](https://pypi.org/project/spectrum/) 0.9.0
- [spglib](https://pypi.org/project/spglib/) 2.6.0
- [sqlalchemy](https://pypi.org/project/sqlalchemy/) 2.0.40
- [srsly](https://pypi.org/project/srsly/) 2.5.1
- [statsmodels](https://pypi.org/project/statsmodels/) 0.14.4
- [ta_lib](https://pypi.org/project/ta_lib/) 0.6.3
- [tables](https://pypi.org/project/tables/) 3.10.2
- [thrift](https://pypi.org/project/thrift/) 0.21.0
- [thriftpy2](https://pypi.org/project/thriftpy2/) 0.5.2
- [tinybrain](https://pypi.org/project/tinybrain/) 1.7.0
- [tokenizers](https://pypi.org/project/tokenizers/) 0.21.1
- [tornado](https://pypi.org/project/tornado/) 6.4.2
- [traits](https://pypi.org/project/traits/) 7.0.2
- [transformations](https://pypi.org/project/transformations/) 2025.1.1
- [treelite](https://pypi.org/project/treelite/) 4.4.1
- [triangle](https://pypi.org/project/triangle/) 20250106
- [tttrlib](https://pypi.org/project/tttrlib/) 0.24.4
- [twisted_iocpsupport](https://pypi.org/project/twisted_iocpsupport/) 25.2.0
- [uharfbuzz](https://pypi.org/project/uharfbuzz/) 0.48.0
- [ujson](https://pypi.org/project/ujson/) 5.10.0
- [VideoCapture](https://videocapture.sourceforge.net/) 0.9.5
- [vidsrc](https://pypi.org/project/vidsrc/) 2025.1.6
- [vispy](https://pypi.org/project/vispy/) 0.14.3
- [wavpack_numcodecs](https://pypi.org/project/wavpack_numcodecs/) 0.2.2
- [wordcloud](https://pypi.org/project/wordcloud/) 1.9.4
- [wrapt](https://pypi.org/project/wrapt/) 1.17.2
- [xgboost](https://pypi.org/project/xgboost/) 2.1.4
- [xxhash](https://pypi.org/project/xxhash/) 3.5.0
- [yappi](https://pypi.org/project/yappi/) 1.6.10
- [yarl](https://pypi.org/project/yarl/) 1.18.3
- [yt](https://pypi.org/project/yt/) 4.4.0
- [zfpy](https://pypi.org/project/zfpy/) 1.0.1
- [zodbpickle](https://pypi.org/project/zodbpickle/) 4.2
- [zope_interface](https://pypi.org/project/zope_interface/) 7.2
- [zope_proxy](https://pypi.org/project/zope_proxy/) 6.1
- [zopflipy](https://pypi.org/project/zopflipy/) 1.11
- [zstandard](https://pypi.org/project/zstandard/) 0.23.0
- [zstd](https://pypi.org/project/zstd/) *******

### Libraries

The wheels may include the following statically or dynamically linked libraries:

- [abseil-cpp](https://github.com/abseil/abseil-cpp) 20240722.0
- [aom](https://aomedia.googlesource.com/aom) 3.12.0
- [arrow](https://github.com/apache/arrow) 1.5.0
- [BerkeleyDB](https://github.com/berkeleydb/libdb/releases/download/v5.3.28/db-5.3.28.tar.gz) 5.3.28
- [boost](https://boostorg.jfrog.io/artifactory/main/release/1.87.0/source/boost_1_87_0.zip) 1.87.0
- [brotli](https://github.com/google/brotli) 1.1.0
- [brunsli](https://github.com/google/brunsli) 0.1
- [bzip2](https://sourceware.org/pub/bzip2/bzip2-1.0.8.tar.gz) 1.0.8
- [c-ares](https://github.com/c-ares/c-ares/releases/download/v1.34.4/c-ares-1.34.4.tar.gz) 1.34.4
- [c-blosc2](https://github.com/Blosc/c-blosc2) 2.17.1
- [c-blosc](https://github.com/Blosc/c-blosc) 1.21.6
- [cairo](https://www.cairographics.org/releases/cairo-1.16.0.tar.xz) 1.16.0+lgpl
- [cfitsio](https://heasarc.gsfc.nasa.gov/FTP/software/fitsio/c/cfitsio-3.49.tar.gz) 3.49
- [charls](https://github.com/team-charls/charls) 2.4.2
- [crc32c](https://github.com/google/crc32c) 1.1.2
- [curl](https://curl.se/download/curl-8.12.1.tar.gz) 8.12.1
- [dav1d](https://github.com/videolan/dav1d) 1.5.1
- [directx-strmbase](https://www.microsoft.com/en-us/download/details.aspx?id=6812) 9.0c
- [eigen](https://gitlab.com/libeigen/eigen/-/archive/3.4.0/eigen-3.4.0.zip) 3.4.0
- [expat](https://github.com/libexpat/libexpat/releases/download/R_2_7_1/expat-2.7.1.tar.gz) 2.7.1
- [flac](https://gitlab.xiph.org/xiph/flac.git) 1.4.3
- [flann](https://github.com/flann-lib/flann) 1.9.2
- [fltk](https://github.com/fltk/fltk/releases/download/release-1.4.2/fltk-1.4.2-source.tar.gz) 1.4.2
- [fmtlib](https://github.com/fmtlib/fmt) 11.1.3
- [freeglut](https://github.com/FreeGLUTProject/freeglut) 3.6.0
- [freetds](https://www.freetds.org/files/stable/freetds-1.4.26.tar.gz) 1.4.26+lgpl
- [freetype](https://download-mirror.savannah.gnu.org/releases/freetype/freetype-2.13.3.tar.gz) 2.13.3
- [freexl](https://www.gaia-gis.it/gaia-sins/freexl-2.0.0.tar.gz) 2.0.0
- [fribidi](https://github.com/fribidi/fribidi) 1.0.15+lgpl
- [gdal](https://github.com/OSGeo/gdal) 3.10.2
- [geos](https://download.osgeo.org/geos/geos-3.13.1.tar.bz2) 3.13.1+lgpl
- [giflib](https://sourceforge.net/projects/giflib/files/giflib-5.2.2.tar.gz) 5.2.2
- [gle](https://sourceforge.net/projects/gle/files/gle/gle-3.1.0/gle-3.1.0.tar.gz) 3.1.0
- [glew](https://github.com/nigels-com/glew/releases/download/glew-2.2.0/glew-2.2.0.tgz) 2.2.0
- [glfw](https://github.com/glfw/glfw) 3.4
- [glm](https://github.com/g-truc/glm) 1.0.1
- [harfbuzz](https://github.com/harfbuzz/harfbuzz) 10.4.0
- [hdf4](https://github.com/HDFGroup/hdf4) 4.3.0
- [hdf5](https://github.com/HDFGroup/hdf5/releases/download/hdf5_1.14.6/hdf5-1.14.6.tar.gz) 1.14.6
- [icu4c](https://github.com/unicode-org/icu/releases/download/release-77-1/icu4c-77_1-src.zip) 77.1
- [imath](https://github.com/AcademySoftwareFoundation/Imath) 3.1.12
- [isa-l](https://github.com/intel/isa-l) 2.31.0
- [jasper](https://github.com/jasper-software/jasper) 4.2.4
- [json-c](https://github.com/json-c/json-c) 0.17
- [jxrlib](https://github.com/cgohlke/jxrlib.git) 1.2
- [krb5](http://web.mit.edu/kerberos/dist/krb5/1.20/krb5-1.20.1.tar.gz) 1.20.1
- [lerc](https://github.com/Esri/lerc) 4.0.4
- [libaec](https://gitlab.dkrz.de/k202009/libaec) 1.1.3
- [libarchive](https://github.com/libarchive/libarchive) 3.7.8
- [libavif](https://github.com/AOMediaCodec/libavif) 1.2.1
- [libczi](https://github.com/ZEISS/libczi) main+lgpl
- [libdeflate](https://github.com/ebiggers/libdeflate) 1.23
- [libevent](https://github.com/libevent/libevent) 2.1.12
- [libgeotiff](https://github.com/OSGeo/libgeotiff/releases/download/1.7.4/libgeotiff-1.7.4.tar.gz) 1.7.4
- [libgit2](https://github.com/libgit2/libgit2) 1.9.0
- [libgta](https://marlam.de/gta/releases/libgta-1.2.1.tar.xz) 1.2.1+lgpl
- [libics](https://github.com/svi-opensource/libics) 1.6.8
- [libjpeg-turbo](https://github.com/libjpeg-turbo/libjpeg-turbo) 3.1.0
- [libjxl](https://github.com/libjxl/libjxl) 0.11.1
- [libjxs](https://standards.iso.org/iso-iec/21122/-5/ed-2/en/ISO_IEC_21122-5_2_Ed-2.zip) 2.0.2
- [libkml](https://github.com/libkml/libkml) 1.3.0
- [liblzf](http://dist.schmorp.de/liblzf/liblzf-3.6.tar.gz) 3.6
- [libmikmod](https://github.com/sezero/mikmod.git) ********+lgpl
- [libmodplug](https://github.com/Konstanty/libmodplug) master
- [libpng](https://github.com/glennrp/libpng) 1.6.47
- [libraqm](https://github.com/HOST-Oman/libraqm) 0.10.2
- [LibRaw](https://github.com/LibRaw/LibRaw) 0.21.3+lgpl
- [libsndfile](https://github.com/libsndfile/libsndfile) 1.2.2+lgpl
- [libsodium](https://download.libsodium.org/libsodium/releases/libsodium-1.0.20-stable.tar.gz) 1.0.20
- [libspatialite](http://www.gaia-gis.it/gaia-sins/libspatialite-sources/libspatialite-5.1.0.tar.gz) 5.1.0
- [libssh2](https://www.libssh2.org/download/libssh2-1.11.1.tar.gz) 1.11.1
- [libtiff](https://gitlab.com/libtiff/libtiff) 4.7.0
- [libultrahdr](https://github.com/google/libultrahdr) 1.3.1
- [libwebp](https://github.com/webmproject/libwebp) 1.5.0
- [libxml2](https://gitlab.gnome.org/GNOME/libxml2) 2.12.10
- [libxslt](https://gitlab.gnome.org/GNOME/libxslt) 1.1.42
- [libyaml](https://github.com/yaml/libyaml) 0.2.5
- [libyuv](https://chromium.googlesource.com/libyuv/libyuv) main
- [libzmq](https://github.com/zeromq/libzmq) 4.3.5
- [littlecms](https://github.com/mm2/Little-CMS) 2.17
- [llvm](https://github.com/llvm/llvm-project/releases/download/llvmorg-15.0.7/llvm-15.0.7.src.tar.xz) 15.0.7
- [lz4](https://github.com/lz4/lz4) 1.10.0
- [lzfse](https://github.com/lzfse/lzfse/) 1.0
- [lzham](https://github.com/richgel999/lzham_codec) master
- [lzma](https://github.com/tukaani-project/xz) 5.6.4
- [lzokay](https://github.com/AxioDL/lzokay) master
- [mimalloc](https://github.com/microsoft/mimalloc) 2.2.2
- [minizip-ng](https://github.com/zlib-ng/minizip-ng) 4.0.7
- [mmtf-cpp](https://github.com/rcsb/mmtf-cpp) 1.1.0
- [mozjpeg](https://github.com/mozilla/mozjpeg) 4.1.5
- [msgpack-c](https://github.com/msgpack/msgpack-c) 6.0.2
- [netcdf-c](https://github.com/Unidata/netcdf-c) 4.9.3
- [nghttp2](https://github.com/nghttp2/nghttp2) 1.65.0
- [ogg](https://gitlab.xiph.org/xiph/ogg.git) 1.3.5
- [oneTBB](https://github.com/oneapi-src/oneTBB) 2022.0.0
- [openblas](https://github.com/xianyi/OpenBLAS/releases/download/v0.3.29/OpenBLAS-0.3.29.zip) 0.3.29
- [opencl-icd-loader](https://github.com/KhronosGroup/OpenCL-ICD-Loader) 2024.10.24
- [OpenColorIO](https://github.com/AcademySoftwareFoundation/OpenColorIO) 2.4.2
- [opencv-python](https://github.com/opencv/opencv-python) ********
- [opencv](https://github.com/opencv/opencv) 4.10.0
- [openexr](https://github.com/AcademySoftwareFoundation/openexr) 3.3.3
- [openhtj2k](https://github.com/osamu620/OpenHTJ2K) v0.2.8
- [OpenImageIO](https://github.com/OpenImageIO/oiio) *******
- [openjpeg](https://github.com/uclouvain/openjpeg) 2.5.3
- [OpenJPH](https://github.com/aous72/OpenJPH) 0.21.2
- [openldap](https://www.openldap.org/software/download/OpenLDAP/openldap-release/openldap-2.4.59.tgz) 2.4.59
- [openssl](https://github.com/openssl/openssl) 3.0.16
- [OpenVDB](https://github.com/AcademySoftwareFoundation/openvdb) 10.0.1
- [OpenVR](https://github.com/ValveSoftware/openvr) 1.0.17
- [opus](https://gitlab.xiph.org/xiph/opus.git) 1.5.2
- [opusfile](https://github.com/xiph/opusfile) 0.12
- [pcodec](https://github.com/mwlon/pcodec) 0.3.1
- [pcre2](https://github.com/PCRE2Project/pcre2.git) 10.44
- [pdcurses](https://github.com/wmcbrine/PDCurses) 3.9
- [pixman](https://www.cairographics.org/releases/pixman-0.42.2.tar.gz) 0.42.2+lgpl
- [pkgconf](https://distfiles.dereferenced.org/pkgconf/pkgconf-1.9.5.tar.xz) 1.9.5
- [portaudio](http://files.portaudio.com/archives/pa_stable_v190700_20210406.tgz) 19.7
- [portmidi](https://sourceforge.net/projects/portmedia/files/portmidi/v2.0.2/portmidi-v2.0.2.zip) 2.0.2
- [postgresql](https://ftp.postgresql.org/pub/source/v15.9/postgresql-15.9.tar.gz) 15.9
- [proj](https://download.osgeo.org/proj/proj-9.5.1.tar.gz) 9.5.1
- [protobuf](https://github.com/protocolbuffers/protobuf/releases/download/v21.12/protobuf-python-4.21.12.zip) 4.21.12
- [ptex](https://github.com/wdas/ptex) 2.4.3
- [pugixml](https://github.com/zeux/pugixml) 1.15
- [pystring](https://github.com/imageworks/pystring) 1.14
- [qhull](https://github.com/qhull/qhull) 8.0.2
- [rapidjson](https://github.com/Tencent/rapidjson) master
- [rav1e](https://github.com/xiph/rav1e) 0.7.1
- [rdkit](https://github.com/rdkit/rdkit) 24.9.5
- [re2](https://github.com/google/re2) 2024.07.02
- [robin-map](https://github.com/Tessil/robin-map) 1.3.0
- [rtmidi](http://www.music.mcgill.ca/~gary/rtmidi/release/rtmidi-6.0.0.tar.gz) 6.0.0
- [sdl](https://github.com/libsdl-org/SDL) 2.32.2
- [sdl_image](https://github.com/libsdl-org/SDL_image) 2.8.8
- [sdl_mixer](https://github.com/libsdl-org/SDL_mixer) 2.8.1
- [sdl_ttf](https://github.com/libsdl-org/SDL_ttf) 2.24.0
- [shapelib](https://github.com/OSGeo/shapelib/releases/download/v1.6.1/shapelib-1.6.1.tar.gz) 1.6.1
- [sheenbidi](https://github.com/Tehreer/SheenBidi) 2.8
- [snappy](https://github.com/google/snappy) 1.2.2
- [spatialindex](https://github.com/libspatialindex/libspatialindex/releases/download/2.1.0/spatialindex-src-2.1.0.tar.gz) 2.1.0
- [sperr](https://github.com/NCAR/SPERR) 0.8.2
- [sqlite](https://github.com/sqlite/sqlite) 3.49.1
- [SVT-AV1](https://gitlab.com/AOMediaCodec/SVT-AV1) 2.3.0
- [SVT-HEVC](https://github.com/OpenVisualCloud/SVT-HEVC) 1.5.1
- [sz3](https://github.com/szcompressor/SZ3) 3.1.8
- [sz](https://github.com/szcompressor/SZ) ********
- [ta-lib](https://sourceforge.net/projects/ta-lib/files/ta-lib/0.4.0/ta-lib-0.4.0-msvc.zip) 0.4.0
- [thrift](https://github.com/apache/thrift) 0.19.0
- [tinyexr](https://github.com/syoyo/tinyexr) 1.0.12
- [treelite](https://github.com/dmlc/treelite) 4.4.1
- [udunits](https://artifacts.unidata.ucar.edu/repository/downloads-udunits/2.2.28/udunits-2.2.28.zip) 2.2.28
- [uriparser](https://github.com/uriparser/uriparser) 0.9.8
- [uthash](https://github.com/troydhanson/uthash) 2.3.0
- [vorbis](https://gitlab.xiph.org/xiph/vorbis.git) 1.3.7
- [vtk-m](https://gitlab.kitware.com/vtk/vtk-m/-/archive/v1.9.0/vtk-m-v1.9.0.tar.gz) 1.9.0
- [WavPack](https://github.com/dbry/WavPack) 5.7.0
- [win-iconv](https://github.com/OgreTransporter/win-iconv) master
- [winflexbison](https://github.com/lexxmark/winflexbison) 2.5.25
- [winpty](https://github.com/rprichard/winpty) master
- [xgboost](https://github.com/dmlc/xgboost) 2.1.4
- [yaml-cpp](https://github.com/jbeder/yaml-cpp) 0.7.0
- [zfp](https://github.com/LLNL/zfp) 1.0.1
- [zlib-ng](https://github.com/zlib-ng/zlib-ng) 2.2.4
- [zlib](https://github.com/madler/zlib) 1.3.1
- [zopfli](https://github.com/google/zopfli) 1.0.3
- [zstd](https://github.com/facebook/zstd) 1.5.7

### Build system

- [Windows Dev Kit](https://learn.microsoft.com/en-us/windows/arm/dev-kit/) 2023
- [Visual Studio](https://visualstudio.microsoft.com/vs/community/) 2022 Community 17.13
- [Python](https://www.python.org/downloads/release/python-3132/) 3.13.2 (ARM64)
- [LLVM](https://github.com/llvm/llvm-project/releases/tag/llvmorg-20.1.1) 20.1.1-woa64
- [Rust](https://www.rust-lang.org/tools/install) 1.85.1
- [Arm Performance Libraries](https://developer.arm.com/downloads/-/arm-performance-libraries) 24.10 (not used)
- [OpenJDK](https://learn.microsoft.com/en-us/java/openjdk/download) 21.0.6 LTS
- [Strawberry Perl](https://strawberryperl.com/) 5.32.1.1
- [MSYS2](https://www.msys2.org/) 20221028
- [Git](https://gitforwindows.org/) 2.47.1
- [CMake](https://cmake.org/download/) 3.31.6
- [DirectX SDK](https://www.microsoft.com/en-us/download/details.aspx?id=6812) 9.0c
