/**
 * 🪟 Window Management System - True Floating Widget Interface
 * Manages multiple floating windows with drag, resize, minimize, close
 */

class WindowManager {
  constructor() {
    this.windows = new Map();
    this.zIndex = 10001;
    this.dragState = {
      isDragging: false,
      currentWindow: null,
      startX: 0,
      startY: 0,
      startLeft: 0,
      startTop: 0
    };
    
    this.init();
  }

  init() {
    // Make Agent Lee card draggable
    this.makeAgentLeeDraggable();
    
    // Add global event listeners
    document.addEventListener('mousemove', this.handleMouseMove.bind(this));
    document.addEventListener('mouseup', this.handleMouseUp.bind(this));
    
    console.log('[WindowManager] Initialized floating widget system');
  }

  makeAgentLeeDraggable() {
    const agentCard = document.getElementById('agent-card');
    const cardHeader = agentCard?.querySelector('.card-header');
    
    if (cardHeader) {
      cardHeader.addEventListener('mousedown', (e) => {
        this.startDrag(e, agentCard);
      });
      
      // Prevent text selection during drag
      cardHeader.style.userSelect = 'none';
      cardHeader.style.webkitUserSelect = 'none';
    }
  }

  createWindow(options = {}) {
    const {
      title = 'Window',
      content = '',
      width = 350,
      height = 300,
      x = 100,
      y = 100,
      resizable = true,
      minimizable = true,
      closable = true
    } = options;

    const windowId = `window-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Create window HTML
    const windowHtml = `
      <div id="${windowId}" class="floating-window" style="width:${width}px;height:${height}px;left:${x}px;top:${y}px;">
        <div class="window-header">
          <span class="window-title">${title}</span>
          <div class="window-controls">
            ${minimizable ? '<button class="window-control-btn window-minimize" title="Minimize">−</button>' : ''}
            ${closable ? '<button class="window-control-btn window-close" title="Close">×</button>' : ''}
          </div>
        </div>
        <div class="window-content">${content}</div>
        ${resizable ? '<div class="window-resize-handle"></div>' : ''}
      </div>
    `;
    
    // Add to DOM
    document.body.insertAdjacentHTML('beforeend', windowHtml);
    const windowElement = document.getElementById(windowId);
    
    // Store window data
    const windowData = {
      id: windowId,
      element: windowElement,
      title,
      isMinimized: false,
      originalHeight: height,
      zIndex: this.zIndex++
    };
    
    this.windows.set(windowId, windowData);
    
    // Set initial z-index
    windowElement.style.zIndex = windowData.zIndex;
    
    // Add event listeners
    this.addWindowEventListeners(windowData);
    
    // Focus window
    this.focusWindow(windowId);
    
    console.log(`[WindowManager] Created window: ${title}`);
    return windowData;
  }

  addWindowEventListeners(windowData) {
    const { element, id } = windowData;
    const header = element.querySelector('.window-header');
    const minimizeBtn = element.querySelector('.window-minimize');
    const closeBtn = element.querySelector('.window-close');
    const resizeHandle = element.querySelector('.window-resize-handle');

    // Make draggable
    if (header) {
      header.addEventListener('mousedown', (e) => {
        // Don't drag if clicking on control buttons
        if (e.target.classList.contains('window-control-btn')) return;
        this.startDrag(e, element);
      });
    }

    // Minimize functionality
    if (minimizeBtn) {
      minimizeBtn.addEventListener('click', () => {
        this.toggleMinimize(id);
      });
    }

    // Close functionality
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.closeWindow(id);
      });
    }

    // Focus on click
    element.addEventListener('mousedown', () => {
      this.focusWindow(id);
    });

    // Resize functionality
    if (resizeHandle) {
      this.addResizeHandle(element, resizeHandle);
    }
  }

  addResizeHandle(windowElement, resizeHandle) {
    // Add resize handle styles
    resizeHandle.style.cssText = `
      position: absolute;
      bottom: 0;
      right: 0;
      width: 15px;
      height: 15px;
      cursor: se-resize;
      background: linear-gradient(-45deg, transparent 40%, #475569 40%, #475569 60%, transparent 60%);
    `;

    let isResizing = false;
    let startX, startY, startWidth, startHeight;

    resizeHandle.addEventListener('mousedown', (e) => {
      isResizing = true;
      startX = e.clientX;
      startY = e.clientY;
      startWidth = parseInt(window.getComputedStyle(windowElement).width, 10);
      startHeight = parseInt(window.getComputedStyle(windowElement).height, 10);
      
      e.preventDefault();
      e.stopPropagation();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isResizing) return;
      
      const newWidth = startWidth + (e.clientX - startX);
      const newHeight = startHeight + (e.clientY - startY);
      
      windowElement.style.width = Math.max(250, newWidth) + 'px';
      windowElement.style.height = Math.max(150, newHeight) + 'px';
    });

    document.addEventListener('mouseup', () => {
      isResizing = false;
    });
  }

  startDrag(e, element) {
    this.dragState.isDragging = true;
    this.dragState.currentWindow = element;
    this.dragState.startX = e.clientX;
    this.dragState.startY = e.clientY;
    this.dragState.startLeft = parseInt(element.style.left || 0, 10);
    this.dragState.startTop = parseInt(element.style.top || 0, 10);
    
    // Prevent text selection
    e.preventDefault();
    
    // Add dragging class for visual feedback
    element.classList.add('dragging');
    document.body.style.cursor = 'move';
  }

  handleMouseMove(e) {
    if (!this.dragState.isDragging || !this.dragState.currentWindow) return;
    
    const deltaX = e.clientX - this.dragState.startX;
    const deltaY = e.clientY - this.dragState.startY;
    
    const newLeft = this.dragState.startLeft + deltaX;
    const newTop = this.dragState.startTop + deltaY;
    
    // Keep window within viewport bounds
    const maxLeft = window.innerWidth - this.dragState.currentWindow.offsetWidth;
    const maxTop = window.innerHeight - this.dragState.currentWindow.offsetHeight;
    
    this.dragState.currentWindow.style.left = Math.max(0, Math.min(maxLeft, newLeft)) + 'px';
    this.dragState.currentWindow.style.top = Math.max(0, Math.min(maxTop, newTop)) + 'px';
  }

  handleMouseUp() {
    if (this.dragState.isDragging) {
      this.dragState.currentWindow?.classList.remove('dragging');
      document.body.style.cursor = '';
      
      this.dragState.isDragging = false;
      this.dragState.currentWindow = null;
    }
  }

  focusWindow(windowId) {
    const windowData = this.windows.get(windowId);
    if (!windowData) return;
    
    // Bring to front
    windowData.zIndex = this.zIndex++;
    windowData.element.style.zIndex = windowData.zIndex;
    
    // Add focus class
    document.querySelectorAll('.floating-window').forEach(w => w.classList.remove('focused'));
    windowData.element.classList.add('focused');
  }

  toggleMinimize(windowId) {
    const windowData = this.windows.get(windowId);
    if (!windowData) return;
    
    const { element } = windowData;
    
    if (windowData.isMinimized) {
      // Restore
      element.style.height = windowData.originalHeight + 'px';
      element.querySelector('.window-content').style.display = 'block';
      windowData.isMinimized = false;
    } else {
      // Minimize
      windowData.originalHeight = element.offsetHeight;
      element.style.height = '40px'; // Just header height
      element.querySelector('.window-content').style.display = 'none';
      windowData.isMinimized = true;
    }
  }

  closeWindow(windowId) {
    const windowData = this.windows.get(windowId);
    if (!windowData) return;
    
    // Add close animation
    windowData.element.style.transform = 'scale(0.8)';
    windowData.element.style.opacity = '0';
    windowData.element.style.transition = 'all 0.2s ease';
    
    setTimeout(() => {
      windowData.element.remove();
      this.windows.delete(windowId);
      console.log(`[WindowManager] Closed window: ${windowData.title}`);
    }, 200);
  }

  // Video Conference Window
  createVideoConferenceWindow() {
    const videoContent = `
      <div class="video-container">
        <video id="local-video" autoplay muted playsinline></video>
        <video id="remote-video" autoplay playsinline></video>
      </div>
      <div class="video-controls">
        <button class="video-control-btn" id="toggle-camera">📹 Camera</button>
        <button class="video-control-btn" id="toggle-mic">🎤 Mic</button>
        <button class="video-control-btn" id="end-call">📞 End</button>
      </div>
    `;

    const window = this.createWindow({
      title: '📹 Video Conference',
      content: videoContent,
      width: 400,
      height: 320,
      x: 200,
      y: 150
    });

    // Initialize video conference
    this.initializeVideoConference(window);
    return window;
  }

  async initializeVideoConference(window) {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: true, 
        audio: true 
      });
      
      const localVideo = window.element.querySelector('#local-video');
      if (localVideo) {
        localVideo.srcObject = stream;
      }

      // Add control event listeners
      const toggleCamera = window.element.querySelector('#toggle-camera');
      const toggleMic = window.element.querySelector('#toggle-mic');
      const endCall = window.element.querySelector('#end-call');

      if (toggleCamera) {
        toggleCamera.addEventListener('click', () => {
          const videoTrack = stream.getVideoTracks()[0];
          if (videoTrack) {
            videoTrack.enabled = !videoTrack.enabled;
            toggleCamera.textContent = videoTrack.enabled ? '📹 Camera' : '📹 Off';
          }
        });
      }

      if (toggleMic) {
        toggleMic.addEventListener('click', () => {
          const audioTrack = stream.getAudioTracks()[0];
          if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            toggleMic.textContent = audioTrack.enabled ? '🎤 Mic' : '🎤 Off';
          }
        });
      }

      if (endCall) {
        endCall.addEventListener('click', () => {
          stream.getTracks().forEach(track => track.stop());
          this.closeWindow(window.id);
        });
      }

      console.log('[WindowManager] Video conference initialized');
    } catch (error) {
      console.error('[WindowManager] Failed to initialize video conference:', error);
      
      // Show error in window
      const content = window.element.querySelector('.window-content');
      content.innerHTML = `
        <div style="text-align: center; padding: 20px;">
          <p>❌ Camera access denied or unavailable</p>
          <p style="font-size: 12px; color: #94a3b8;">Please allow camera permissions and try again</p>
        </div>
      `;
    }
  }

  // Eye Tracking Window
  createEyeTrackingWindow() {
    const eyeContent = `
      <canvas id="gaze-canvas"></canvas>
      <div style="position: absolute; bottom: 10px; left: 10px; font-size: 12px; color: #94a3b8;">
        Eye tracking active
      </div>
    `;

    const window = this.createWindow({
      title: '👁️ Eye Tracking',
      content: eyeContent,
      width: 320,
      height: 240,
      x: 300,
      y: 200
    });

    this.initializeEyeTracking(window);
    return window;
  }

  async initializeEyeTracking(window) {
    try {
      const canvas = window.element.querySelector('#gaze-canvas');
      const ctx = canvas.getContext('2d');
      
      // Set canvas size
      canvas.width = 280;
      canvas.height = 180;
      
      // Simple eye tracking simulation
      let gazeX = canvas.width / 2;
      let gazeY = canvas.height / 2;
      
      function drawGaze() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw background
        ctx.fillStyle = '#000';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Draw gaze point
        ctx.beginPath();
        ctx.arc(gazeX, gazeY, 10, 0, 2 * Math.PI);
        ctx.fillStyle = '#00ff88';
        ctx.fill();
        
        // Draw crosshair
        ctx.strokeStyle = '#00ff88';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(gazeX - 15, gazeY);
        ctx.lineTo(gazeX + 15, gazeY);
        ctx.moveTo(gazeX, gazeY - 15);
        ctx.lineTo(gazeX, gazeY + 15);
        ctx.stroke();
        
        // Simulate eye movement
        gazeX += (Math.random() - 0.5) * 4;
        gazeY += (Math.random() - 0.5) * 4;
        
        // Keep within bounds
        gazeX = Math.max(20, Math.min(canvas.width - 20, gazeX));
        gazeY = Math.max(20, Math.min(canvas.height - 20, gazeY));
        
        requestAnimationFrame(drawGaze);
      }
      
      drawGaze();
      console.log('[WindowManager] Eye tracking initialized');
    } catch (error) {
      console.error('[WindowManager] Failed to initialize eye tracking:', error);
    }
  }

  // Object Recognition Window
  createObjectRecognitionWindow() {
    const recognitionContent = `
      <div class="recognition-display">
        <video id="recognition-video" autoplay playsinline style="width: 100%; height: 100%;"></video>
        <div class="recognition-overlay" id="recognition-overlay"></div>
      </div>
    `;

    const window = this.createWindow({
      title: '🔍 Object Recognition',
      content: recognitionContent,
      width: 380,
      height: 300,
      x: 400,
      y: 250
    });

    this.initializeObjectRecognition(window);
    return window;
  }

  async initializeObjectRecognition(window) {
    try {
      const video = window.element.querySelector('#recognition-video');
      const overlay = window.element.querySelector('#recognition-overlay');
      
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      video.srcObject = stream;
      
      // Simulate object detection
      setInterval(() => {
        // Clear previous detections
        overlay.innerHTML = '';
        
        // Add random detection boxes
        const numDetections = Math.floor(Math.random() * 3) + 1;
        const objects = ['Person', 'Face', 'Hand', 'Phone', 'Cup', 'Book'];
        
        for (let i = 0; i < numDetections; i++) {
          const box = document.createElement('div');
          box.className = 'detection-box';
          
          const x = Math.random() * 250;
          const y = Math.random() * 180;
          const w = 50 + Math.random() * 100;
          const h = 40 + Math.random() * 80;
          
          box.style.left = x + 'px';
          box.style.top = y + 'px';
          box.style.width = w + 'px';
          box.style.height = h + 'px';
          
          const label = document.createElement('div');
          label.className = 'detection-label';
          label.textContent = objects[Math.floor(Math.random() * objects.length)];
          box.appendChild(label);
          
          overlay.appendChild(box);
        }
      }, 2000);
      
      console.log('[WindowManager] Object recognition initialized');
    } catch (error) {
      console.error('[WindowManager] Failed to initialize object recognition:', error);
    }
  }

  // Get all windows
  getAllWindows() {
    return Array.from(this.windows.values());
  }

  // Close all windows
  closeAllWindows() {
    this.windows.forEach((windowData, id) => {
      this.closeWindow(id);
    });
  }
}

// Export for use
if (typeof window !== 'undefined') {
  window.WindowManager = WindowManager;
  console.log('[WindowManager] Window management system loaded');
}
