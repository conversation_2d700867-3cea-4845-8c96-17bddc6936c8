#!/usr/bin/env python3
"""
Agent Lee™ System - Frontend Startup Script
Opens the Agent Lee interface in the default browser
"""

import webbrowser
import os
import time
from pathlib import Path

def main():
    """Start the Agent Lee frontend interface"""
    print("🎨 Starting Agent Lee™ Frontend Interface")
    print("=" * 50)
    
    # Get the path to index.html
    frontend_path = Path(__file__).parent / "index.html"
    
    if not frontend_path.exists():
        print(f"❌ Frontend file not found: {frontend_path}")
        print("Make sure index.html exists in the project root")
        return
    
    # Convert to file URL
    file_url = f"file:///{frontend_path.absolute().as_posix()}"
    
    print(f"🌐 Opening Agent Lee interface...")
    print(f"📁 Location: {file_url}")
    print("=" * 50)
    
    # Open in default browser
    try:
        webbrowser.open(file_url)
        print("✅ Agent Lee interface opened in browser")
        print("💡 Make sure the backend is running (python start.py)")
        print("🔗 Backend should be at: http://localhost:8000")
    except Exception as e:
        print(f"❌ Failed to open browser: {e}")
        print(f"📋 Manual URL: {file_url}")

if __name__ == "__main__":
    main()
