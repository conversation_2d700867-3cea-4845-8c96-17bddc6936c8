"""
Agent Lee™ System Configuration
Environment-based configuration management
"""

import os
from typing import Optional
from pathlib import Path

class AppConfig:
    """Application configuration management"""
    
    # Server Configuration
    HOST: str = os.getenv("AGENTLEE_HOST", "127.0.0.1")
    PORT: int = int(os.getenv("AGENTLEE_PORT", "8000"))
    DEBUG: bool = os.getenv("AGENTLEE_DEBUG", "False").lower() == "true"
    
    # Security Configuration
    ALLOWED_ORIGINS: list = os.getenv(
        "ALLOWED_ORIGINS", 
        "http://localhost:3000,http://localhost:8000,http://127.0.0.1:8000,file://"
    ).split(",")
    
    # API Keys
    GEMINI_API_KEY: Optional[str] = os.getenv("GEMINI_API_KEY")
    OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
    ANTHROPIC_API_KEY: Optional[str] = os.getenv("ANTHROPIC_API_KEY")
    
    # Database Configuration
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./agentlee.db")
    
    # Ollama Configuration (Local LLM fallback)
    OLLAMA_BASE_URL: str = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    OLLAMA_MODEL: str = os.getenv("OLLAMA_MODEL", "llama2")
    
    # Logging Configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "agentlee.log")
    
    # TTS Configuration
    TTS_ENGINE: str = os.getenv("TTS_ENGINE", "pyttsx3")
    TTS_VOICE: str = os.getenv("TTS_VOICE", "default")
    TTS_RATE: float = float(os.getenv("TTS_RATE", "1.0"))
    
    # Voice Recognition Configuration
    VOICE_RECOGNITION_TIMEOUT: int = int(os.getenv("VOICE_RECOGNITION_TIMEOUT", "5"))
    VOICE_RECOGNITION_PHRASE_TIMEOUT: int = int(os.getenv("VOICE_RECOGNITION_PHRASE_TIMEOUT", "1"))
    
    @classmethod
    def get_config_summary(cls) -> dict:
        """Get configuration summary for logging"""
        return {
            "host": cls.HOST,
            "port": cls.PORT,
            "debug": cls.DEBUG,
            "database": cls.DATABASE_URL,
            "gemini_configured": bool(cls.GEMINI_API_KEY),
            "ollama_url": cls.OLLAMA_BASE_URL,
            "log_level": cls.LOG_LEVEL
        }
    
    @classmethod
    def validate_config(cls) -> list:
        """Validate configuration and return any issues"""
        issues = []
        
        if not cls.GEMINI_API_KEY:
            issues.append("GEMINI_API_KEY not configured - LLM features may be limited")
        
        if not Path(cls.DATABASE_URL.replace("sqlite:///", "")).parent.exists():
            issues.append("Database directory does not exist")
        
        return issues
