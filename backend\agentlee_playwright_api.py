
from fastapi import FastAP<PERSON>
from pydantic import BaseModel
import subprocess
import asyncio
from playwright.async_api import async_playwright

app = FastAPI()

class SearchQuery(BaseModel):
    query: str

class ClickAction(BaseModel):
    selector: str

@app.post("/api/open_browser")
async def open_browser():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        await page.goto("https://www.google.com")
        await asyncio.sleep(5)
        await browser.close()
    return {"status": "<PERSON><PERSON><PERSON> opened"}

@app.post("/api/search_google")
async def search_google(payload: SearchQuery):
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        await page.goto("https://www.google.com")
        await page.fill("input[name=q]", payload.query)
        await page.press("input[name=q]", "Enter")
        await asyncio.sleep(5)
        await browser.close()
    return {"status": "Search complete", "query": payload.query}

@app.post("/api/click_element")
async def click_element(payload: ClickAction):
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        await page.goto("https://www.google.com")
        await page.click(payload.selector)
        await asyncio.sleep(5)
        await browser.close()
    return {"status": f"Clicked {payload.selector}"}
