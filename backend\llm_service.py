"""
Agent Lee™ LLM Service
Multi-provider LLM integration with personality engine
"""

import os
import logging
from typing import Optional, Dict, Any
from config import AppConfig

logger = logging.getLogger("agentlee.llm")

class LLMService:
    """Multi-provider LLM service with personality engine"""
    
    def __init__(self):
        self.primary_provider = None
        self.fallback_providers = []
        self.personality_mode = "default"
        self.initialize_providers()
    
    def initialize_providers(self):
        """Initialize available LLM providers"""
        try:
            # Try Google Gemini first
            if AppConfig.GEMINI_API_KEY:
                import google.generativeai as genai
                genai.configure(api_key=AppConfig.GEMINI_API_KEY)
                self.primary_provider = "gemini"
                logger.info("✅ Gemini API initialized successfully")
            
            # Add other providers as fallbacks
            if AppConfig.OPENAI_API_KEY:
                self.fallback_providers.append("openai")
                logger.info("✅ OpenAI available as fallback")
            
            if AppConfig.ANTHROPIC_API_KEY:
                self.fallback_providers.append("anthropic")
                logger.info("✅ Anthropic available as fallback")
            
            # Always add Ollama as final fallback
            self.fallback_providers.append("ollama")
            logger.info("✅ Ollama available as fallback")
            
        except Exception as e:
            logger.error(f"❌ LLM initialization error: {e}")
    
    async def generate_response(self, message: str, context: Optional[str] = None) -> Dict[str, Any]:
        """Generate response using available LLM providers"""
        try:
            # Apply personality to the message
            enhanced_message = self._apply_personality(message, context)
            
            # Try primary provider first
            if self.primary_provider == "gemini":
                return await self._generate_gemini_response(enhanced_message)
            
            # Try fallback providers
            for provider in self.fallback_providers:
                try:
                    if provider == "openai":
                        return await self._generate_openai_response(enhanced_message)
                    elif provider == "anthropic":
                        return await self._generate_anthropic_response(enhanced_message)
                    elif provider == "ollama":
                        return await self._generate_ollama_response(enhanced_message)
                except Exception as e:
                    logger.warning(f"Provider {provider} failed: {e}")
                    continue
            
            # Final fallback - demo response
            return self._generate_demo_response(message)
            
        except Exception as e:
            logger.error(f"❌ LLM generation error: {e}")
            return self._generate_demo_response(message)
    
    async def _generate_gemini_response(self, message: str) -> Dict[str, Any]:
        """Generate response using Google Gemini"""
        try:
            import google.generativeai as genai
            model = genai.GenerativeModel('gemini-1.5-flash')
            response = model.generate_content(message)
            
            return {
                "response": response.text,
                "provider": "gemini",
                "personality": self.personality_mode,
                "success": True
            }
        except Exception as e:
            logger.error(f"Gemini error: {e}")
            raise
    
    async def _generate_openai_response(self, message: str) -> Dict[str, Any]:
        """Generate response using OpenAI"""
        try:
            import openai
            openai.api_key = AppConfig.OPENAI_API_KEY
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": message}]
            )
            
            return {
                "response": response.choices[0].message.content,
                "provider": "openai",
                "personality": self.personality_mode,
                "success": True
            }
        except Exception as e:
            logger.error(f"OpenAI error: {e}")
            raise
    
    async def _generate_anthropic_response(self, message: str) -> Dict[str, Any]:
        """Generate response using Anthropic Claude"""
        try:
            import anthropic
            client = anthropic.Anthropic(api_key=AppConfig.ANTHROPIC_API_KEY)
            
            response = client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=1000,
                messages=[{"role": "user", "content": message}]
            )
            
            return {
                "response": response.content[0].text,
                "provider": "anthropic",
                "personality": self.personality_mode,
                "success": True
            }
        except Exception as e:
            logger.error(f"Anthropic error: {e}")
            raise
    
    async def _generate_ollama_response(self, message: str) -> Dict[str, Any]:
        """Generate response using Ollama (local)"""
        try:
            import requests
            response = requests.post(
                f"{AppConfig.OLLAMA_BASE_URL}/api/generate",
                json={
                    "model": AppConfig.OLLAMA_MODEL,
                    "prompt": message,
                    "stream": False
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "response": result.get("response", ""),
                    "provider": "ollama",
                    "personality": self.personality_mode,
                    "success": True
                }
            else:
                raise Exception(f"Ollama API error: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Ollama error: {e}")
            raise
    
    def _generate_demo_response(self, message: str) -> Dict[str, Any]:
        """Generate demo response when no LLM is available"""
        demo_responses = {
            "default": f"Hello! I'm Agent Lee. I received your message: '{message}'. I'm currently running in demo mode. Please configure an LLM provider for full functionality.",
            "funny": f"Haha! You said '{message}' and I'm like... well, I would totally have a witty comeback if my LLM was working! 😄",
            "serious": f"I acknowledge your message: '{message}'. Currently operating in demonstration mode. Please configure proper LLM integration.",
            "excited": f"OMG! You said '{message}'! That's SO cool! I wish I could give you an amazing response but I'm in demo mode! 🎉",
            "chill": f"Hey there... you mentioned '{message}'... that's cool, man. I'm just chilling in demo mode right now... 😎"
        }
        
        return {
            "response": demo_responses.get(self.personality_mode, demo_responses["default"]),
            "provider": "demo",
            "personality": self.personality_mode,
            "success": True
        }
    
    def _apply_personality(self, message: str, context: Optional[str] = None) -> str:
        """Apply personality context to the message"""
        personality_prompts = {
            "default": "You are Agent Lee, a helpful AI assistant. Respond naturally and helpfully.",
            "funny": "You are Agent Lee, a witty and humorous AI assistant. Add humor and jokes to your responses while being helpful.",
            "serious": "You are Agent Lee, a professional and serious AI assistant. Provide formal, detailed, and precise responses.",
            "excited": "You are Agent Lee, an enthusiastic and energetic AI assistant. Show excitement and use emojis in your responses!",
            "chill": "You are Agent Lee, a relaxed and laid-back AI assistant. Keep responses casual and cool."
        }
        
        personality_context = personality_prompts.get(self.personality_mode, personality_prompts["default"])
        enhanced_message = f"{personality_context}\n\nUser message: {message}"
        
        if context:
            enhanced_message += f"\n\nContext: {context}"
        
        return enhanced_message
    
    def set_personality(self, mode: str):
        """Set personality mode"""
        valid_modes = ["default", "funny", "serious", "excited", "chill"]
        if mode in valid_modes:
            self.personality_mode = mode
            logger.info(f"Personality mode set to: {mode}")
        else:
            logger.warning(f"Invalid personality mode: {mode}")
    
    def get_available_models(self) -> list:
        """Get list of available models"""
        models = []
        if self.primary_provider:
            models.append(f"{self.primary_provider} (primary)")
        for provider in self.fallback_providers:
            models.append(f"{provider} (fallback)")
        return models
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get service status"""
        return {
            "primary_provider": self.primary_provider,
            "fallback_providers": self.fallback_providers,
            "personality_mode": self.personality_mode,
            "available_models": self.get_available_models()
        }

# Global LLM service instance
llm_service = LLMService()
