# Module 13: Enhanced Application Controller
import subprocess
import os
import platform
import logging

logger = logging.getLogger("agentlee.mods.app_controller")

class EnhancedAppController:
    """Enhanced application controller with improved app opening capabilities"""

    def __init__(self):
        self.platform = platform.system().lower()
        self.app_registry = self._build_app_registry()
        logger.info("Enhanced App Controller initialized")

    def _build_app_registry(self):
        """Build enhanced app registry with more applications"""
        if self.platform == "windows":
            return {
                # System apps
                "notepad": "notepad.exe",
                "calculator": "calc.exe",
                "paint": "mspaint.exe",
                "cmd": "cmd.exe",
                "powershell": "powershell.exe",
                "explorer": "explorer.exe",

                # Browsers
                "chrome": r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                "firefox": r"C:\Program Files\Mozilla Firefox\firefox.exe",
                "edge": "msedge.exe",

                # Office apps
                "word": "winword.exe",
                "excel": "excel.exe",
                "powerpoint": "powerpnt.exe",
                "outlook": "outlook.exe",

                # Communication
                "teams": "ms-teams:",
                "discord": "discord:",
                "telegram": "tg:",
                "whatsapp": "whatsapp:",
                "skype": "skype:",

                # Media & Entertainment
                "spotify": "spotify:",
                "vlc": r"C:\Program Files\VideoLAN\VLC\vlc.exe",
                "steam": "steam:",

                # Development
                "vscode": "code",
                "sublime": "subl",
                "notepadpp": r"C:\Program Files\Notepad++\notepad++.exe"
            }
        return {}

    def open_application(self, app_name: str):
        """Enhanced app opening with better error handling"""
        try:
            app_name = app_name.lower().strip()

            if app_name in self.app_registry:
                app_path = self.app_registry[app_name]

                if app_path.startswith(("http:", "https:", "mailto:", "ms-", "tg:", "discord:", "whatsapp:", "spotify:", "steam:", "skype:")):
                    # Protocol handler
                    os.startfile(app_path)
                    logger.info(f"✅ Opened {app_name} via protocol handler")
                else:
                    # Regular executable
                    subprocess.Popen(app_path, shell=True)
                    logger.info(f"✅ Opened {app_name} application")

                return {"status": "success", "app": app_name, "message": f"Opened {app_name}"}
            else:
                # Try to find app in system PATH
                try:
                    subprocess.Popen([app_name])
                    logger.info(f"✅ Opened {app_name} from PATH")
                    return {"status": "success", "app": app_name, "message": f"Opened {app_name}"}
                except FileNotFoundError:
                    logger.warning(f"❌ Application '{app_name}' not found")
                    return {"status": "error", "message": f"Application '{app_name}' not found"}

        except Exception as e:
            logger.error(f"❌ Error opening {app_name}: {e}")
            return {"status": "error", "message": f"Failed to open {app_name}: {str(e)}"}

    def get_available_apps(self):
        """Get list of available applications"""
        return list(self.app_registry.keys())

# Initialize the enhanced app controller
enhanced_app_controller = EnhancedAppController()
print('✅ Enhanced Application Controller loaded with improved app opening capabilities')