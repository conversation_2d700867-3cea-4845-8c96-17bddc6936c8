/**
 * 📊 Optimization Dashboard - Real-time system monitoring UI
 * Visual dashboard for the Agentic Optimization System
 */

class OptimizationDashboard {
  constructor(optimizationSystem) {
    this.aos = optimizationSystem;
    this.isVisible = false;
    this.updateInterval = null;
    this.charts = new Map();
    
    this.createDashboard();
    this.initializeCharts();
    this.startUpdating();
  }

  createDashboard() {
    // Create dashboard container
    this.container = document.createElement('div');
    this.container.id = 'optimization-dashboard';
    this.container.className = 'optimization-dashboard hidden';
    
    this.container.innerHTML = `
      <div class="dashboard-header">
        <h3>🧠 Agent Lee Optimization Dashboard</h3>
        <div class="optimization-score">
          <span class="score-label">Optimization Index:</span>
          <span class="score-value" id="optimization-score">100</span>
          <span class="score-status" id="optimization-status">Optimal</span>
        </div>
        <button class="dashboard-toggle" onclick="window.optimizationDashboard.toggle()">×</button>
      </div>
      
      <div class="dashboard-content">
        <div class="metrics-grid">
          <!-- Memory Metrics -->
          <div class="metric-card">
            <h4>🧠 Memory Usage</h4>
            <div class="metric-value">
              <span id="memory-pressure">0%</span>
              <div class="progress-bar">
                <div class="progress-fill" id="memory-progress"></div>
              </div>
            </div>
            <div class="metric-details">
              <small>Allocated: <span id="memory-allocated">0 MB</span></small>
              <small>Total: <span id="memory-total">0 MB</span></small>
            </div>
          </div>

          <!-- Performance Metrics -->
          <div class="metric-card">
            <h4>⚡ Performance</h4>
            <div class="metric-value">
              <span id="performance-latency">0ms</span>
              <div class="progress-bar">
                <div class="progress-fill" id="performance-progress"></div>
              </div>
            </div>
            <div class="metric-details">
              <small>Load Time: <span id="load-time">0ms</span></small>
              <small>Throughput: <span id="throughput">0 ops/s</span></small>
            </div>
          </div>

          <!-- Worker Health -->
          <div class="metric-card">
            <h4>👷 Worker Health</h4>
            <div class="metric-value">
              <span id="worker-health">100%</span>
              <div class="progress-bar">
                <div class="progress-fill" id="worker-progress"></div>
              </div>
            </div>
            <div class="metric-details">
              <small>Active: <span id="workers-active">0</span></small>
              <small>Failed: <span id="workers-failed">0</span></small>
            </div>
          </div>

          <!-- Task Queue -->
          <div class="metric-card">
            <h4>📋 Task Queue</h4>
            <div class="metric-value">
              <span id="queue-congestion">0%</span>
              <div class="progress-bar">
                <div class="progress-fill" id="queue-progress"></div>
              </div>
            </div>
            <div class="metric-details">
              <small>Pending: <span id="tasks-pending">0</span></small>
              <small>Processing: <span id="tasks-processing">0</span></small>
            </div>
          </div>
        </div>

        <!-- Agent Status -->
        <div class="agents-section">
          <h4>🤖 Agent Clusters</h4>
          <div class="agents-grid" id="agents-grid">
            <!-- Agent status cards will be populated here -->
          </div>
        </div>

        <!-- Real-time Chart -->
        <div class="chart-section">
          <h4>📈 Real-time Metrics</h4>
          <canvas id="metrics-chart" width="400" height="200"></canvas>
        </div>

        <!-- Recommendations -->
        <div class="recommendations-section">
          <h4>💡 Optimization Recommendations</h4>
          <div id="recommendations-list" class="recommendations-list">
            <!-- Recommendations will be populated here -->
          </div>
        </div>
      </div>
    `;

    // Add styles
    this.addStyles();
    
    // Append to body
    document.body.appendChild(this.container);
    
    // Make globally accessible
    window.optimizationDashboard = this;
  }

  addStyles() {
    const styles = `
      <style>
        .optimization-dashboard {
          position: fixed;
          top: 20px;
          right: 20px;
          width: 450px;
          max-height: 80vh;
          background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
          border: 2px solid #00f2ff;
          border-radius: 15px;
          box-shadow: 0 20px 40px rgba(0, 242, 255, 0.3);
          color: white;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          z-index: 9999;
          overflow-y: auto;
          transition: all 0.3s ease;
        }

        .optimization-dashboard.hidden {
          transform: translateX(100%);
          opacity: 0;
        }

        .dashboard-header {
          padding: 15px 20px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .dashboard-header h3 {
          margin: 0;
          font-size: 16px;
          color: #00f2ff;
        }

        .optimization-score {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
        }

        .score-value {
          font-weight: bold;
          font-size: 18px;
          color: #00ff88;
        }

        .score-status {
          padding: 2px 8px;
          border-radius: 10px;
          background: rgba(0, 255, 136, 0.2);
          font-size: 12px;
        }

        .dashboard-toggle {
          background: none;
          border: none;
          color: white;
          font-size: 20px;
          cursor: pointer;
          padding: 5px;
          border-radius: 50%;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .dashboard-toggle:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        .dashboard-content {
          padding: 20px;
        }

        .metrics-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px;
          margin-bottom: 20px;
        }

        .metric-card {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 10px;
          padding: 15px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-card h4 {
          margin: 0 0 10px 0;
          font-size: 14px;
          color: #00f2ff;
        }

        .metric-value {
          margin-bottom: 10px;
        }

        .metric-value span {
          font-size: 18px;
          font-weight: bold;
          color: #00ff88;
        }

        .progress-bar {
          width: 100%;
          height: 6px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
          margin-top: 5px;
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #00ff88, #00f2ff);
          border-radius: 3px;
          transition: width 0.3s ease;
        }

        .metric-details {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
        }

        .agents-section, .chart-section, .recommendations-section {
          margin-bottom: 20px;
        }

        .agents-section h4, .chart-section h4, .recommendations-section h4 {
          margin: 0 0 10px 0;
          font-size: 14px;
          color: #00f2ff;
        }

        .agents-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 10px;
        }

        .agent-card {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          padding: 10px;
          text-align: center;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .agent-status {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          display: inline-block;
          margin-right: 5px;
        }

        .agent-status.healthy { background: #00ff88; }
        .agent-status.warning { background: #ffaa00; }
        .agent-status.critical { background: #ff4444; }

        #metrics-chart {
          width: 100%;
          height: 150px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
        }

        .recommendations-list {
          max-height: 100px;
          overflow-y: auto;
        }

        .recommendation-item {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 6px;
          padding: 8px 12px;
          margin-bottom: 5px;
          font-size: 12px;
          border-left: 3px solid #00f2ff;
        }
      </style>
    `;
    
    document.head.insertAdjacentHTML('beforeend', styles);
  }

  initializeCharts() {
    // Initialize simple canvas-based chart
    this.canvas = document.getElementById('metrics-chart');
    this.ctx = this.canvas.getContext('2d');
    this.chartData = {
      memory: [],
      performance: [],
      timestamps: []
    };
  }

  startUpdating() {
    this.updateInterval = setInterval(() => {
      this.updateMetrics();
      this.updateChart();
      this.updateAgentStatus();
      this.updateRecommendations();
    }, 1000);
  }

  updateMetrics() {
    const metrics = this.aos.getMetrics();
    
    // Update optimization score
    document.getElementById('optimization-score').textContent = Math.round(metrics.optimizationIndex);
    document.getElementById('optimization-status').textContent = metrics.health.status;
    
    // Update memory metrics
    const memoryPressure = Math.round(metrics.memory.pressure * 100);
    document.getElementById('memory-pressure').textContent = `${memoryPressure}%`;
    document.getElementById('memory-progress').style.width = `${memoryPressure}%`;
    document.getElementById('memory-allocated').textContent = `${Math.round(metrics.memory.allocated / 1024 / 1024)} MB`;
    document.getElementById('memory-total').textContent = `${Math.round(metrics.memory.total / 1024 / 1024)} MB`;
    
    // Update performance metrics
    document.getElementById('performance-latency').textContent = `${Math.round(metrics.performance.latency)}ms`;
    const perfProgress = Math.min(100, (metrics.performance.latency / 200) * 100);
    document.getElementById('performance-progress').style.width = `${perfProgress}%`;
    document.getElementById('load-time').textContent = `${Math.round(metrics.performance.loadTime)}ms`;
    
    // Update worker health
    const totalWorkers = metrics.workers.active + metrics.workers.idle + metrics.workers.failed;
    const workerHealth = totalWorkers > 0 ? Math.round((metrics.workers.active / totalWorkers) * 100) : 100;
    document.getElementById('worker-health').textContent = `${workerHealth}%`;
    document.getElementById('worker-progress').style.width = `${workerHealth}%`;
    document.getElementById('workers-active').textContent = metrics.workers.active;
    document.getElementById('workers-failed').textContent = metrics.workers.failed;
  }

  updateChart() {
    const metrics = this.aos.getMetrics();
    
    // Add new data point
    this.chartData.memory.push(metrics.memory.pressure * 100);
    this.chartData.performance.push(Math.min(100, (metrics.performance.latency / 200) * 100));
    this.chartData.timestamps.push(Date.now());
    
    // Keep only last 50 data points
    if (this.chartData.memory.length > 50) {
      this.chartData.memory.shift();
      this.chartData.performance.shift();
      this.chartData.timestamps.shift();
    }
    
    // Draw chart
    this.drawChart();
  }

  drawChart() {
    const ctx = this.ctx;
    const width = this.canvas.width;
    const height = this.canvas.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    if (this.chartData.memory.length < 2) return;
    
    // Draw memory line
    ctx.strokeStyle = '#00ff88';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    this.chartData.memory.forEach((value, index) => {
      const x = (index / (this.chartData.memory.length - 1)) * width;
      const y = height - (value / 100) * height;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
    
    // Draw performance line
    ctx.strokeStyle = '#00f2ff';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    this.chartData.performance.forEach((value, index) => {
      const x = (index / (this.chartData.performance.length - 1)) * width;
      const y = height - (value / 100) * height;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
  }

  updateAgentStatus() {
    const agentsGrid = document.getElementById('agents-grid');
    agentsGrid.innerHTML = '';
    
    this.aos.agents.forEach((agent, name) => {
      const agentCard = document.createElement('div');
      agentCard.className = 'agent-card';
      
      const status = this.getAgentStatus(name);
      agentCard.innerHTML = `
        <div class="agent-status ${status}"></div>
        <div style="font-size: 12px; margin-top: 5px;">${name}</div>
      `;
      
      agentsGrid.appendChild(agentCard);
    });
  }

  getAgentStatus(agentName) {
    // Simplified status logic
    const metrics = this.aos.getMetrics();
    
    if (metrics.optimizationIndex > 80) return 'healthy';
    if (metrics.optimizationIndex > 50) return 'warning';
    return 'critical';
  }

  updateRecommendations() {
    const recommendationsList = document.getElementById('recommendations-list');
    const metrics = this.aos.getMetrics();
    
    const recommendations = [];
    
    if (metrics.memory.pressure > 0.8) {
      recommendations.push('High memory usage detected - consider clearing caches');
    }
    
    if (metrics.performance.latency > 100) {
      recommendations.push('High latency detected - optimize event handlers');
    }
    
    if (metrics.optimizationIndex < 70) {
      recommendations.push('System performance below optimal - run deep optimization');
    }
    
    recommendationsList.innerHTML = recommendations.map(rec => 
      `<div class="recommendation-item">${rec}</div>`
    ).join('');
  }

  toggle() {
    this.isVisible = !this.isVisible;
    this.container.classList.toggle('hidden', !this.isVisible);
  }

  show() {
    this.isVisible = true;
    this.container.classList.remove('hidden');
  }

  hide() {
    this.isVisible = false;
    this.container.classList.add('hidden');
  }

  destroy() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    
    delete window.optimizationDashboard;
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = OptimizationDashboard;
}
