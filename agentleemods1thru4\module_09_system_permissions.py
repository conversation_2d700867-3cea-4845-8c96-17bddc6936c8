# Module 09: Enhanced System Permissions Handler
import os
import sys
import platform
import logging
import json
from pathlib import Path

logger = logging.getLogger("agentlee.mods.permissions")

class SystemPermissionsManager:
    """Enhanced system permissions management"""

    def __init__(self):
        self.platform = platform.system().lower()
        self.permissions_file = Path("agentleemods1thru4/permission_log.json")
        self.granted_permissions = self._load_permissions()
        logger.info("System Permissions Manager initialized")

    def _load_permissions(self):
        """Load previously granted permissions"""
        try:
            if self.permissions_file.exists():
                with open(self.permissions_file, 'r') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.warning(f"Failed to load permissions: {e}")
            return {}

    def _save_permissions(self):
        """Save granted permissions to file"""
        try:
            self.permissions_file.parent.mkdir(exist_ok=True)
            with open(self.permissions_file, 'w') as f:
                json.dump(self.granted_permissions, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save permissions: {e}")

    def request_permission(self, permission_type: str, description: str = ""):
        """Request a specific permission"""
        if permission_type in self.granted_permissions:
            logger.info(f"Permission '{permission_type}' already granted")
            return True

        # For now, auto-grant permissions (in production, this would show UI)
        self.granted_permissions[permission_type] = {
            "granted": True,
            "description": description,
            "timestamp": str(Path().stat().st_mtime)
        }
        self._save_permissions()
        logger.info(f"Permission '{permission_type}' granted: {description}")
        return True

    def check_permission(self, permission_type: str):
        """Check if a permission is granted"""
        return self.granted_permissions.get(permission_type, {}).get("granted", False)

    def get_all_permissions(self):
        """Get all granted permissions"""
        return self.granted_permissions

    def revoke_permission(self, permission_type: str):
        """Revoke a specific permission"""
        if permission_type in self.granted_permissions:
            del self.granted_permissions[permission_type]
            self._save_permissions()
            logger.info(f"❌ Permission '{permission_type}' revoked")
            return True
        return False

# Initialize the permissions manager
permissions_manager = SystemPermissionsManager()

# Request essential permissions
permissions_manager.request_permission("file_access", "Access local files and directories")
permissions_manager.request_permission("app_control", "Open and control applications")
permissions_manager.request_permission("system_info", "Read system information and status")
permissions_manager.request_permission("web_access", "Open web browsers and URLs")

print('Enhanced System Permissions Handler loaded with permission management')