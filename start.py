#!/usr/bin/env python3
"""
Agent Lee™ System - Single Startup Script
The ONLY way to start Agent Lee™ System v2.0
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path

# Set UTF-8 encoding for Windows console compatibility
if sys.platform == "win32":
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# Load environment variables FIRST
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("[OK] Environment variables loaded")
except ImportError:
    print("[WARN] python-dotenv not available - environment variables not loaded")
except Exception as e:
    print(f"[WARN] Failed to load environment variables: {e}")

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("[ERROR] Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    print(f"[OK] Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        ('fastapi', 'fastapi'),
        ('uvicorn', 'uvicorn'),
        ('pydantic', 'pydantic'),
        ('psutil', 'psutil'),
        ('sqlalchemy', 'sqlalchemy'),
        ('python-dotenv', 'dotenv'),
        ('google-generativeai', 'google.generativeai')
    ]

    missing = []
    for package_name, import_name in required_packages:
        if importlib.util.find_spec(import_name) is None:
            missing.append(package_name)
        else:
            print(f"[OK] {package_name}")

    if missing:
        print(f"\n[ERROR] Missing packages: {', '.join(missing)}")
        print("   Install with: pip install -r backend/requirements.txt")
        return False

    return True

def check_structure():
    """Check if project structure is correct"""
    required_files = [
        'backend/agentlee_controller_v2.py',
        'backend/config.py',
        'backend/models.py',
        'backend/llm_service.py',
        'backend/.env.example'
    ]

    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"[ERROR] Missing required file: {file_path}")
            return False
        else:
            print(f"[OK] {file_path}")

    return True

def setup_environment():
    """Setup environment configuration"""
    env_file = Path('.env')
    env_example = Path('backend/.env.example')

    if not env_file.exists():
        if env_example.exists():
            print("[WARN] .env file not found. Creating from backend/.env.example...")
            try:
                with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                    dst.write(src.read())
                print("[OK] .env file created. Please edit it with your configuration.")
            except Exception as e:
                print(f"[ERROR] Failed to create .env file: {e}")
                return False
        else:
            print("[ERROR] No .env.example file found")
            return False
    else:
        print("[OK] .env file found")

    return True

def start_server():
    """Start the Agent Lee server"""
    print("\n[INFO] Starting Agent Lee System v2.0...")
    print("=" * 50)

    try:
        # Add backend to Python path
        backend_path = Path(__file__).parent / 'backend'
        sys.path.insert(0, str(backend_path))

        # Import and start
        from agentlee_controller_v2 import app
        from config import AppConfig
        import uvicorn

        print(f"[INFO] Server starting at http://{AppConfig.HOST}:{AppConfig.PORT}")
        print("[INFO] API documentation: http://localhost:8000/docs")
        print("[INFO] Press Ctrl+C to stop")
        print("-" * 50)

        # Start the server
        uvicorn.run(
            app,
            host=AppConfig.HOST,
            port=AppConfig.PORT,
            reload=AppConfig.DEBUG,
            log_level=AppConfig.LOG_LEVEL.lower()
        )

    except KeyboardInterrupt:
        print("\n[INFO] Server stopped by user")
    except Exception as e:
        print(f"[ERROR] Server startup failed: {e}")
        return False

    return True

def main():
    """Main startup routine - THE ONLY WAY TO START AGENT LEE"""
    print("Agent Lee System v2.0 - Single Startup")
    print("=" * 50)

    # Check Python version
    if not check_python_version():
        sys.exit(1)

    # Check project structure
    if not check_structure():
        print("\n[ERROR] Project structure is incomplete")
        sys.exit(1)

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    # Setup environment
    if not setup_environment():
        sys.exit(1)

    # Start server
    start_server()

if __name__ == "__main__":
    main()
