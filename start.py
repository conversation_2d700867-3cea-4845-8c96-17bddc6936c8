#!/usr/bin/env python3
"""
Agent Lee™ System - Backend Startup Script
Starts the FastAPI backend server
"""

import os
import sys
from pathlib import Path

# Add backend to Python path
backend_path = Path(__file__).parent / 'backend'
sys.path.insert(0, str(backend_path))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("[OK] Environment variables loaded")
except ImportError:
    print("[WARN] python-dotenv not available")
except Exception as e:
    print(f"[WARN] Failed to load environment variables: {e}")

def main():
    """Start the Agent Lee backend server"""
    try:
        # Import and start the backend
        from agentlee_controller_v2 import app
        from config import AppConfig
        import uvicorn

        print(f"🚀 Starting Agent Lee™ Backend Server")
        print(f"📡 Server: http://{AppConfig.HOST}:{AppConfig.PORT}")
        print(f"📚 API Docs: http://{AppConfig.HOST}:{AppConfig.PORT}/docs")
        print("=" * 50)

        # Start the server
        uvicorn.run(
            app,
            host=AppConfig.HOST,
            port=AppConfig.PORT,
            reload=AppConfig.DEBUG,
            log_level=AppConfig.LOG_LEVEL.lower()
        )

    except KeyboardInterrupt:
        print("\n[INFO] Backend server stopped")
    except Exception as e:
        print(f"[ERROR] Backend startup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
